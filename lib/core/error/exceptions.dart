/// Base exception class for error handling
abstract class AppException implements Exception {
  final String message;
  final int? code;

  const AppException({
    required this.message,
    this.code,
  });

  @override
  String toString() => message;
}

/// Server exception for API errors
class ServerException extends AppException {
  const ServerException({
    required super.message,
    super.code,
  });
}

/// Network exception for connectivity issues
class NetworkException extends AppException {
  const NetworkException({
    required super.message,
    super.code,
  });
}

/// Cache exception for local storage issues
class CacheException extends AppException {
  const CacheException({
    required super.message,
    super.code,
  });
}

/// Validation exception for input validation errors
class ValidationException extends AppException {
  const ValidationException({
    required super.message,
    super.code,
  });
}

/// Authentication exception for auth errors
class AuthException extends AppException {
  const AuthException({
    required super.message,
    super.code,
  });
}
