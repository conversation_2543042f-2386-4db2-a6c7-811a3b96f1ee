/// Base exception class for the application
class AppException implements Exception {
  final String message;
  
  AppException({required this.message});
  
  @override
  String toString() => message;
}

/// Exception for server errors
class ServerException extends AppException {
  ServerException({required super.message});
}

/// Exception for network errors
class NetworkException extends AppException {
  NetworkException({required super.message});
}

/// Exception for cache errors
class CacheException extends AppException {
  CacheException({required super.message});
}

/// Exception for unauthorized access
class UnauthorizedException extends AppException {
  UnauthorizedException({required super.message});
}

/// Exception for forbidden access
class ForbiddenException extends AppException {
  ForbiddenException({required super.message});
}

/// Exception for not found resources
class NotFoundException extends AppException {
  NotFoundException({required super.message});
}

/// Exception for validation errors
class ValidationException extends AppException {
  final Map<String, dynamic>? errors;
  
  ValidationException({required super.message, this.errors});
}

/// Exception for timeout errors
class TimeoutException extends AppException {
  TimeoutException({required super.message});
}
