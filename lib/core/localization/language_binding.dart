import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'language_controller.dart';

/// LanguageBinding for binding language controller dependencies
class LanguageBinding extends Bindings {
  @override
  void dependencies() {
    // Use Get.put instead of Get.lazyPut to ensure immediate initialization
    // This ensures the controller is available immediately when needed
    if (!Get.isRegistered<LanguageController>()) {
      Get.put<LanguageController>(
        LanguageController(prefs: Get.find<SharedPreferences>()),
        permanent: true, // Make it permanent so it's not garbage collected
      );
    }
  }
}
