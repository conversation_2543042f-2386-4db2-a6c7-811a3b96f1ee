import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../utils/logger.dart';

/// LanguageController for managing language state
/// Following Single Responsibility Principle by focusing only on language management
class LanguageController extends GetxController {
  static const String languageKey = 'language_code';

  final SharedPreferences _prefs;

  // Observable state
  final RxString _currentLanguage = 'en'.obs;

  // Getters
  String get currentLanguage => _currentLanguage.value;
  bool get isArabic => _currentLanguage.value == 'ar';
  Locale get locale => Locale(_currentLanguage.value);

  // Constructor with dependency injection
  LanguageController({required SharedPreferences prefs}) : _prefs = prefs;

  @override
  void onInit() {
    super.onInit();
    _loadLanguageFromPrefs();
  }

  /// Load language from SharedPreferences
  void _loadLanguageFromPrefs() {
    // Try to get language from the new location first
    String? languageCode = _prefs.getString(languageKey);

    // If not found, try to get it from the legacy 'lang' key used by CacheHelper
    if (languageCode == null || languageCode.isEmpty) {
      languageCode = _prefs.getString('lang');

      // If we found a language in the legacy location, also save it to the new location
      if (languageCode != null && languageCode.isNotEmpty) {
        _prefs.setString(languageKey, languageCode);
        LoggerService.debug(
          'Language migrated from legacy storage to new location',
          data: {'languageCode': languageCode},
        );
      }
    }

    // Set the language if we found it
    if (languageCode != null && languageCode.isNotEmpty) {
      _currentLanguage.value = languageCode;
      LoggerService.debug(
        'Language loaded successfully',
        data: {'languageCode': languageCode},
      );
    } else {
      // Default to Arabic if no language is found (as per original app)
      _currentLanguage.value = 'ar';
      LoggerService.debug('No language found in storage, defaulting to Arabic');
    }
  }

  /// Change language
  Future<void> changeLanguage(String languageCode) async {
    if (languageCode == _currentLanguage.value) return;

    // Save language to both locations to ensure compatibility
    await _prefs.setString(languageKey, languageCode);
    await _prefs.setString('lang', languageCode);

    LoggerService.debug(
      'Language changed and saved to storage',
      data: {'languageCode': languageCode},
    );

    // Update observable state
    _currentLanguage.value = languageCode;

    // Update app locale
    Get.updateLocale(Locale(languageCode));
  }

  /// Toggle between English and Arabic
  Future<void> toggleLanguage() async {
    final String newLanguage = isArabic ? 'en' : 'ar';
    await changeLanguage(newLanguage);
  }
}
