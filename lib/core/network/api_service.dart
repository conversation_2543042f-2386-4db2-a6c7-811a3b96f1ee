import 'package:dio/dio.dart' as dio;
import 'package:get/get.dart';
import '../constants/app_constants.dart';
import '../errors/exceptions.dart';
import '../utils/logger.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// ApiService class for handling API requests
/// Following Single Responsibility Principle by focusing only on API communication
class ApiService extends GetxService {
  late final dio.Dio _dio;

  // Observable token for reactive updates
  final RxString _token = ''.obs;

  // Getters
  String get token => _token.value;

  /// Initialize the API service
  Future<ApiService> init() async {
    LoggerService.info(
      'Initializing API Service with base URL: ${AppConstants.baseUrl}',
    );

    _dio = dio.Dio(
      dio.BaseOptions(
        baseUrl: AppConstants.baseUrl,
        connectTimeout: Duration(milliseconds: AppConstants.connectionTimeout),
        receiveTimeout: Duration(milliseconds: AppConstants.receiveTimeout),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          // Add language header (same as SchoolX mobile app)
          'ln': Get.locale?.languageCode == 'ar' ? 'ar' : 'en',
          // Add CORS headers for web
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
          'Access-Control-Allow-Headers':
              'Origin, Content-Type, X-Auth-Token, ln, Authorization',
        },
        // Enable CORS for web
        validateStatus: (status) {
          return status! < 500;
        },
      ),
    );

    LoggerService.debug(
      'Dio instance created with timeouts',
      data: {
        'connectTimeout': '${AppConstants.connectionTimeout}ms',
        'receiveTimeout': '${AppConstants.receiveTimeout}ms',
      },
    );

    // Configure for web to handle CORS
    if (GetPlatform.isWeb) {
      LoggerService.info('Configuring Dio for web platform');

      // Add CORS handling for web
      _dio.options.extra['withCredentials'] = true;
    }

    // Add interceptors for logging and token management
    _dio.interceptors.add(
      dio.InterceptorsWrapper(
        onRequest: (options, handler) async {
          // Try to get token from memory or SharedPreferences
          String token = _token.value;
          if (token.isEmpty) {
            try {
              final prefs = Get.find<SharedPreferences>();

              // Try to get token from SharedPreferences using AppConstants.tokenKey
              String? storedToken = prefs.getString(AppConstants.tokenKey);

              // If token is not found, try to get it from the legacy "token" key used by CacheHelper
              if (storedToken == null || storedToken.isEmpty) {
                storedToken = prefs.getString("token");

                // If we found a token in the legacy location, also save it to the new location
                if (storedToken != null && storedToken.isNotEmpty) {
                  await prefs.setString(AppConstants.tokenKey, storedToken);
                  LoggerService.debug(
                    'Token migrated from legacy storage to new location during request',
                    data: {'tokenLength': storedToken.length},
                  );
                }
              }

              if (storedToken != null && storedToken.isNotEmpty) {
                token = storedToken;
                _token.value = token; // Update in-memory token
                LoggerService.debug(
                  'Token loaded from storage for request',
                  data: {'tokenLength': token.length},
                );
              }
            } catch (e) {
              LoggerService.error(
                'Failed to load token from storage for request',
                error: e,
              );
            }
          }

          // Add auth token if available (using the same format as SchoolX mobile app)
          if (token.isNotEmpty) {
            options.headers['Authorization'] = 'Bearer $token';
            // Update language header on each request to ensure it's current
            options.headers['ln'] =
                Get.locale?.languageCode == 'ar' ? 'ar' : 'en';
            LoggerService.debug(
              'Adding auth token to request',
              data: {
                'tokenPresent': true,
                'tokenLength': token.length,
                'language': options.headers['ln'],
              },
            );
          } else {
            LoggerService.debug('No auth token available for request');
            // Still update language header even without token
            options.headers['ln'] =
                Get.locale?.languageCode == 'ar' ? 'ar' : 'en';
          }

          // Log request
          LoggerService.request(
            'REQUEST[${options.method}] => PATH: ${options.path}',
            data: options.data,
            headers: options.headers,
          );

          return handler.next(options);
        },
        onResponse: (response, handler) {
          // Log response
          LoggerService.success(
            'RESPONSE[${response.statusCode}] => PATH: ${response.requestOptions.path}',
            data: response.data,
          );

          return handler.next(response);
        },
        onError: (dio.DioException e, handler) {
          // Log error with more details
          LoggerService.error(
            'ERROR[${e.response?.statusCode}] => PATH: ${e.requestOptions.path}',
            error: {
              'message': e.message,
              'type': e.type.toString(),
              'data': e.response?.data,
              'headers': e.requestOptions.headers,
              'requestData': e.requestOptions.data,
            },
          );

          // Always handle errors properly, no mock data fallback
          LoggerService.error(
            'Connection error occurred, please check your internet connection',
          );

          return handler.next(e);
        },
      ),
    );

    LoggerService.info('API Service interceptors configured');

    // Load token from storage if available
    await _loadToken();

    return this;
  }

  /// Load token from shared preferences
  Future<void> _loadToken() async {
    try {
      final prefs = Get.find<SharedPreferences>();

      // Try to get token from SharedPreferences using AppConstants.tokenKey
      String? token = prefs.getString(AppConstants.tokenKey);

      // If token is not found, try to get it from the legacy "token" key used by CacheHelper
      if (token == null || token.isEmpty) {
        token = prefs.getString("token");

        // If we found a token in the legacy location, also save it to the new location
        if (token != null && token.isNotEmpty) {
          await prefs.setString(AppConstants.tokenKey, token);
          LoggerService.debug(
            'Token migrated from legacy storage to new location',
            data: {'tokenLength': token.length},
          );
        }
      }

      // Set the token if we found it
      if (token != null && token.isNotEmpty) {
        _token.value = token;
        LoggerService.debug(
          'Token loaded successfully',
          data: {'tokenLength': token.length},
        );
      } else {
        LoggerService.debug('No token found in storage');
      }
    } catch (e) {
      LoggerService.error('Failed to load token from storage', error: e);
    }
  }

  /// Set authentication token
  Future<void> setToken(String token) async {
    _token.value = token;
    try {
      final prefs = Get.find<SharedPreferences>();

      // Save token to both locations to ensure compatibility
      await prefs.setString(AppConstants.tokenKey, token);
      await prefs.setString("token", token);

      LoggerService.debug(
        'Token saved to storage in both formats',
        data: {'tokenLength': token.length},
      );
    } catch (e) {
      LoggerService.error('Failed to save token to storage', error: e);
    }
  }

  /// Clear authentication token
  Future<void> clearToken() async {
    _token.value = '';
    try {
      final prefs = Get.find<SharedPreferences>();

      // Remove token from both locations to ensure compatibility
      await prefs.remove(AppConstants.tokenKey);
      await prefs.remove("token");

      LoggerService.debug('Token removed from all storage locations');
    } catch (e) {
      LoggerService.error('Failed to remove token from storage', error: e);
    }
  }

  /// Get current token
  Future<String?> getToken() async {
    if (_token.value.isNotEmpty) {
      return _token.value;
    }

    // Try to load from shared preferences
    try {
      final prefs = Get.find<SharedPreferences>();

      // Try to get token from SharedPreferences using AppConstants.tokenKey
      String? token = prefs.getString(AppConstants.tokenKey);

      // If token is not found, try to get it from the legacy "token" key used by CacheHelper
      if (token == null || token.isEmpty) {
        token = prefs.getString("token");

        // If we found a token in the legacy location, also save it to the new location
        if (token != null && token.isNotEmpty) {
          await prefs.setString(AppConstants.tokenKey, token);
          LoggerService.debug(
            'Token migrated from legacy storage to new location during getToken',
            data: {'tokenLength': token.length},
          );
        }
      }

      if (token != null && token.isNotEmpty) {
        _token.value = token;
        LoggerService.debug(
          'Token loaded from storage',
          data: {'tokenLength': token.length},
        );
        return token;
      }
    } catch (e) {
      LoggerService.error('Failed to load token from storage', error: e);
    }

    return null;
  }

  /// GET request
  Future<dio.Response> get(
    String url, {
    Map<String, dynamic>? queryParameters,
    bool isAuth = false,
  }) async {
    LoggerService.debug(
      'Making GET request',
      data: {'url': url, 'queryParameters': queryParameters, 'isAuth': isAuth},
    );

    try {
      final response = await _dio.get(url, queryParameters: queryParameters);

      LoggerService.debug(
        'GET request successful',
        data: {
          'statusCode': response.statusCode,
          'responseSize':
              response.data != null ? response.data.toString().length : 0,
        },
      );

      return response;
    } on dio.DioException catch (e) {
      LoggerService.error('GET request failed with DioException', error: e);
      _handleDioError(e);
      rethrow;
    } catch (e, stackTrace) {
      LoggerService.error(
        'GET request failed with unexpected error',
        error: e,
        stackTrace: stackTrace,
      );
      throw ServerException(message: e.toString());
    }
  }

  /// POST request
  Future<dio.Response> post({
    required String url,
    dynamic body,
    Map<String, dynamic>? queryParameters,
    bool isAuth = false,
  }) async {
    LoggerService.debug(
      'Making POST request',
      data: {
        'url': url,
        'body': body,
        'queryParameters': queryParameters,
        'isAuth': isAuth,
      },
    );

    try {
      final response = await _dio.post(
        url,
        data: body,
        queryParameters: queryParameters,
      );

      LoggerService.debug(
        'POST request successful',
        data: {
          'statusCode': response.statusCode,
          'responseSize':
              response.data != null ? response.data.toString().length : 0,
        },
      );

      return response;
    } on dio.DioException catch (e) {
      LoggerService.error('POST request failed with DioException', error: e);
      _handleDioError(e);
      rethrow;
    } catch (e, stackTrace) {
      LoggerService.error(
        'POST request failed with unexpected error',
        error: e,
        stackTrace: stackTrace,
      );
      throw ServerException(message: e.toString());
    }
  }

  /// PUT request
  Future<dio.Response> put({
    required String url,
    dynamic body,
    Map<String, dynamic>? queryParameters,
    bool isAuth = false,
  }) async {
    try {
      final response = await _dio.put(
        url,
        data: body,
        queryParameters: queryParameters,
      );
      return response;
    } on dio.DioException catch (e) {
      _handleDioError(e);
      rethrow;
    } catch (e) {
      throw ServerException(message: e.toString());
    }
  }

  /// DELETE request
  Future<dio.Response> delete({
    required String url,
    dynamic body,
    Map<String, dynamic>? queryParameters,
    bool isAuth = false,
  }) async {
    try {
      final response = await _dio.delete(
        url,
        data: body,
        queryParameters: queryParameters,
      );
      return response;
    } on dio.DioException catch (e) {
      _handleDioError(e);
      rethrow;
    } catch (e) {
      throw ServerException(message: e.toString());
    }
  }

  /// Handle Dio errors
  void _handleDioError(dio.DioException e) {
    if (e.type == dio.DioExceptionType.connectionTimeout ||
        e.type == dio.DioExceptionType.receiveTimeout ||
        e.type == dio.DioExceptionType.sendTimeout) {
      throw TimeoutException(message: 'Connection timeout');
    } else if (e.type == dio.DioExceptionType.badResponse) {
      final statusCode = e.response?.statusCode;
      final data = e.response?.data;

      if (statusCode == 401) {
        throw UnauthorizedException(
          message:
              data is Map ? data['message'] ?? 'Unauthorized' : 'Unauthorized',
        );
      } else if (statusCode == 403) {
        throw ForbiddenException(
          message: data is Map ? data['message'] ?? 'Forbidden' : 'Forbidden',
        );
      } else if (statusCode == 404) {
        throw NotFoundException(
          message: data is Map ? data['message'] ?? 'Not found' : 'Not found',
        );
      } else if (statusCode == 422) {
        throw ValidationException(
          message:
              data is Map
                  ? data['message'] ?? 'Validation error'
                  : 'Validation error',
          errors: data is Map && data['errors'] != null ? data['errors'] : null,
        );
      } else {
        throw ServerException(
          message:
              data is Map ? data['message'] ?? 'Server error' : 'Server error',
        );
      }
    } else if (e.type == dio.DioExceptionType.connectionError) {
      throw NetworkException(message: 'No internet connection');
    } else {
      throw ServerException(message: e.message ?? 'Unknown error');
    }
  }
}
