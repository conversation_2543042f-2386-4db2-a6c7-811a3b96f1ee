import 'dart:typed_data';
import 'package:excel/excel.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/foundation.dart';

import '../../domain/entities/student.dart';
import '../utils/logger.dart';

/// Excel service for handling student import/export operations
/// Following Single Responsibility Principle by focusing only on Excel operations
class ExcelService {
  static const int maxFileSize = 10 * 1024 * 1024; // 10MB
  static const List<String> allowedExtensions = ['xlsx', 'xls'];

  /// Column headers that match the original SchoolX Student entity structure
  static const Map<String, String> columnHeaders = {
    'A': 'name',
    'B': 'phone', 
    'C': 'address',
    'D': 'cityName',
    'E': 'dateBirth',
    'F': 'genderId',
    'G': 'religionId',
    'H': 'gradeId',
    'I': 'typeBloodId',
    'J': 'tripType',
  };

  /// Gender mapping for validation
  static const Map<String, int> genderMapping = {
    'Male': 1,
    'Female': 2,
    'male': 1,
    'female': 2,
    'M': 1,
    'F': 2,
  };

  /// Religion mapping for validation
  static const Map<String, int> religionMapping = {
    'Islam': 1,
    'Christianity': 2,
    'Judaism': 3,
    'Other': 4,
    'islam': 1,
    'christianity': 2,
    'judaism': 3,
    'other': 4,
  };

  /// Blood type mapping for validation
  static const Map<String, int> bloodTypeMapping = {
    'A+': 1,
    'A-': 2,
    'B+': 3,
    'B-': 4,
    'AB+': 5,
    'AB-': 6,
    'O+': 7,
    'O-': 8,
  };

  /// Trip type validation
  static const List<String> validTripTypes = [
    'morning',
    'evening',
    'full_day',
  ];

  /// Generate Excel template with proper headers and sample data
  static Uint8List generateTemplate() {
    try {
      final excel = Excel.createExcel();
      final sheet = excel['Students'];

      // Add headers
      sheet.cell(CellIndex.indexByString('A1')).value = TextCellValue('Name *');
      sheet.cell(CellIndex.indexByString('B1')).value = TextCellValue('Phone *');
      sheet.cell(CellIndex.indexByString('C1')).value = TextCellValue('Address *');
      sheet.cell(CellIndex.indexByString('D1')).value = TextCellValue('City Name *');
      sheet.cell(CellIndex.indexByString('E1')).value = TextCellValue('Date of Birth *');
      sheet.cell(CellIndex.indexByString('F1')).value = TextCellValue('Gender ID *');
      sheet.cell(CellIndex.indexByString('G1')).value = TextCellValue('Religion ID *');
      sheet.cell(CellIndex.indexByString('H1')).value = TextCellValue('Grade ID *');
      sheet.cell(CellIndex.indexByString('I1')).value = TextCellValue('Blood Type ID');
      sheet.cell(CellIndex.indexByString('J1')).value = TextCellValue('Trip Type *');

      // Add sample data
      sheet.cell(CellIndex.indexByString('A2')).value = TextCellValue('Ahmed Ali');
      sheet.cell(CellIndex.indexByString('B2')).value = TextCellValue('123456789');
      sheet.cell(CellIndex.indexByString('C2')).value = TextCellValue('123 Main Street');
      sheet.cell(CellIndex.indexByString('D2')).value = TextCellValue('Riyadh');
      sheet.cell(CellIndex.indexByString('E2')).value = TextCellValue('15/06/2010');
      sheet.cell(CellIndex.indexByString('F2')).value = IntCellValue(1);
      sheet.cell(CellIndex.indexByString('G2')).value = IntCellValue(1);
      sheet.cell(CellIndex.indexByString('H2')).value = IntCellValue(5);
      sheet.cell(CellIndex.indexByString('I2')).value = IntCellValue(7);
      sheet.cell(CellIndex.indexByString('J2')).value = TextCellValue('full_day');

      sheet.cell(CellIndex.indexByString('A3')).value = TextCellValue('Sara Mohammed');
      sheet.cell(CellIndex.indexByString('B3')).value = TextCellValue('987654321');
      sheet.cell(CellIndex.indexByString('C3')).value = TextCellValue('456 Oak Avenue');
      sheet.cell(CellIndex.indexByString('D3')).value = TextCellValue('Jeddah');
      sheet.cell(CellIndex.indexByString('E3')).value = TextCellValue('22/03/2011');
      sheet.cell(CellIndex.indexByString('F3')).value = IntCellValue(2);
      sheet.cell(CellIndex.indexByString('G3')).value = IntCellValue(1);
      sheet.cell(CellIndex.indexByString('H3')).value = IntCellValue(4);
      sheet.cell(CellIndex.indexByString('I3')).value = IntCellValue(1);
      sheet.cell(CellIndex.indexByString('J3')).value = TextCellValue('morning');

      // Add instructions sheet
      final instructionsSheet = excel['Instructions'];
      instructionsSheet.cell(CellIndex.indexByString('A1')).value = TextCellValue('Student Import Instructions');
      instructionsSheet.cell(CellIndex.indexByString('A3')).value = TextCellValue('Required Fields (marked with *):');
      instructionsSheet.cell(CellIndex.indexByString('A4')).value = TextCellValue('- Name: Student full name');
      instructionsSheet.cell(CellIndex.indexByString('A5')).value = TextCellValue('- Phone: Contact number');
      instructionsSheet.cell(CellIndex.indexByString('A6')).value = TextCellValue('- Address: Home address');
      instructionsSheet.cell(CellIndex.indexByString('A7')).value = TextCellValue('- City Name: City of residence');
      instructionsSheet.cell(CellIndex.indexByString('A8')).value = TextCellValue('- Date of Birth: DD/MM/YYYY format');
      instructionsSheet.cell(CellIndex.indexByString('A9')).value = TextCellValue('- Gender ID: 1=Male, 2=Female');
      instructionsSheet.cell(CellIndex.indexByString('A10')).value = TextCellValue('- Religion ID: 1=Islam, 2=Christianity, 3=Judaism, 4=Other');
      instructionsSheet.cell(CellIndex.indexByString('A11')).value = TextCellValue('- Grade ID: 1-12 (Grade 1 to Grade 12)');
      instructionsSheet.cell(CellIndex.indexByString('A12')).value = TextCellValue('- Trip Type: morning, evening, or full_day');
      instructionsSheet.cell(CellIndex.indexByString('A14')).value = TextCellValue('Optional Fields:');
      instructionsSheet.cell(CellIndex.indexByString('A15')).value = TextCellValue('- Blood Type ID: 1=A+, 2=A-, 3=B+, 4=B-, 5=AB+, 6=AB-, 7=O+, 8=O-');

      return excel.encode()!;
    } catch (e) {
      LoggerService.error('Error generating Excel template', error: e);
      rethrow;
    }
  }

  /// Pick Excel file from device
  static Future<FilePickerResult?> pickExcelFile() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: allowedExtensions,
        allowMultiple: false,
        withData: true,
      );

      if (result != null && result.files.isNotEmpty) {
        final file = result.files.first;
        
        // Validate file size
        if (file.size > maxFileSize) {
          throw Exception('File size exceeds 10MB limit');
        }

        // Validate file extension
        final extension = file.extension?.toLowerCase();
        if (extension == null || !allowedExtensions.contains(extension)) {
          throw Exception('Invalid file format. Please select .xlsx or .xls file');
        }

        return result;
      }
      return null;
    } catch (e) {
      LoggerService.error('Error picking Excel file', error: e);
      rethrow;
    }
  }

  /// Parse Excel file and extract student data
  static Future<ExcelParseResult> parseExcelFile(Uint8List fileBytes) async {
    try {
      final excel = Excel.decodeBytes(fileBytes);
      final sheet = excel.tables.values.first;
      
      final List<StudentImportData> students = [];
      final List<ExcelError> errors = [];
      
      // Skip header row, start from row 2
      for (int rowIndex = 1; rowIndex < sheet.maxRows; rowIndex++) {
        final row = sheet.rows[rowIndex];
        
        // Skip empty rows
        if (_isRowEmpty(row)) continue;
        
        try {
          final studentData = _parseStudentRow(row, rowIndex + 1);
          students.add(studentData);
        } catch (e) {
          errors.add(ExcelError(
            row: rowIndex + 1,
            message: e.toString(),
          ));
        }
      }

      return ExcelParseResult(
        students: students,
        errors: errors,
        totalRows: sheet.maxRows - 1, // Exclude header
      );
    } catch (e) {
      LoggerService.error('Error parsing Excel file', error: e);
      throw Exception('Failed to parse Excel file: ${e.toString()}');
    }
  }

  /// Check if row is empty
  static bool _isRowEmpty(List<Data?> row) {
    return row.every((cell) => cell?.value == null || cell!.value.toString().trim().isEmpty);
  }

  /// Parse individual student row
  static StudentImportData _parseStudentRow(List<Data?> row, int rowNumber) {
    final data = <String, dynamic>{};
    
    // Extract data from each column
    for (int colIndex = 0; colIndex < row.length && colIndex < columnHeaders.length; colIndex++) {
      final columnKey = columnHeaders.values.elementAt(colIndex);
      final cellValue = row[colIndex]?.value;
      
      if (cellValue != null) {
        data[columnKey] = cellValue.toString().trim();
      }
    }

    // Validate required fields
    _validateRequiredFields(data, rowNumber);
    
    // Validate and convert data types
    _validateAndConvertData(data, rowNumber);

    return StudentImportData.fromMap(data, rowNumber);
  }

  /// Validate required fields
  static void _validateRequiredFields(Map<String, dynamic> data, int rowNumber) {
    final requiredFields = ['name', 'phone', 'address', 'cityName', 'dateBirth', 'genderId', 'religionId', 'gradeId', 'tripType'];
    
    for (final field in requiredFields) {
      if (data[field] == null || data[field].toString().trim().isEmpty) {
        throw Exception('Missing required field: $field');
      }
    }
  }

  /// Validate and convert data types
  static void _validateAndConvertData(Map<String, dynamic> data, int rowNumber) {
    // Validate gender ID
    if (data['genderId'] != null) {
      final genderValue = data['genderId'].toString();
      if (genderMapping.containsKey(genderValue)) {
        data['genderId'] = genderMapping[genderValue];
      } else {
        final genderId = int.tryParse(genderValue);
        if (genderId == null || (genderId != 1 && genderId != 2)) {
          throw Exception('Invalid gender ID. Must be 1 (Male) or 2 (Female)');
        }
        data['genderId'] = genderId;
      }
    }

    // Validate religion ID
    if (data['religionId'] != null) {
      final religionValue = data['religionId'].toString();
      if (religionMapping.containsKey(religionValue)) {
        data['religionId'] = religionMapping[religionValue];
      } else {
        final religionId = int.tryParse(religionValue);
        if (religionId == null || religionId < 1 || religionId > 4) {
          throw Exception('Invalid religion ID. Must be 1-4');
        }
        data['religionId'] = religionId;
      }
    }

    // Validate grade ID
    if (data['gradeId'] != null) {
      final gradeId = int.tryParse(data['gradeId'].toString());
      if (gradeId == null || gradeId < 1 || gradeId > 12) {
        throw Exception('Invalid grade ID. Must be 1-12');
      }
      data['gradeId'] = gradeId;
    }

    // Validate blood type ID (optional)
    if (data['typeBloodId'] != null && data['typeBloodId'].toString().isNotEmpty) {
      final bloodTypeValue = data['typeBloodId'].toString();
      if (bloodTypeMapping.containsKey(bloodTypeValue)) {
        data['typeBloodId'] = bloodTypeMapping[bloodTypeValue];
      } else {
        final bloodTypeId = int.tryParse(bloodTypeValue);
        if (bloodTypeId == null || bloodTypeId < 1 || bloodTypeId > 8) {
          throw Exception('Invalid blood type ID. Must be 1-8');
        }
        data['typeBloodId'] = bloodTypeId;
      }
    }

    // Validate trip type
    if (data['tripType'] != null) {
      final tripType = data['tripType'].toString().toLowerCase();
      if (!validTripTypes.contains(tripType)) {
        throw Exception('Invalid trip type. Must be: morning, evening, or full_day');
      }
      data['tripType'] = tripType;
    }

    // Validate date format
    if (data['dateBirth'] != null) {
      final dateStr = data['dateBirth'].toString();
      if (!_isValidDateFormat(dateStr)) {
        throw Exception('Invalid date format. Use DD/MM/YYYY');
      }
    }
  }

  /// Validate date format (DD/MM/YYYY)
  static bool _isValidDateFormat(String dateStr) {
    final regex = RegExp(r'^\d{1,2}/\d{1,2}/\d{4}$');
    if (!regex.hasMatch(dateStr)) return false;

    try {
      final parts = dateStr.split('/');
      final day = int.parse(parts[0]);
      final month = int.parse(parts[1]);
      final year = int.parse(parts[2]);

      if (day < 1 || day > 31) return false;
      if (month < 1 || month > 12) return false;
      if (year < 1900 || year > DateTime.now().year) return false;

      return true;
    } catch (e) {
      return false;
    }
  }
}

/// Result of Excel parsing operation
class ExcelParseResult {
  final List<StudentImportData> students;
  final List<ExcelError> errors;
  final int totalRows;

  ExcelParseResult({
    required this.students,
    required this.errors,
    required this.totalRows,
  });

  bool get hasErrors => errors.isNotEmpty;
  int get successCount => students.length;
  int get errorCount => errors.length;
}

/// Excel parsing error
class ExcelError {
  final int row;
  final String message;

  ExcelError({
    required this.row,
    required this.message,
  });
}

/// Student import data structure
class StudentImportData {
  final String name;
  final String phone;
  final String address;
  final String cityName;
  final String dateBirth;
  final int genderId;
  final int religionId;
  final int gradeId;
  final int? typeBloodId;
  final String tripType;
  final int rowNumber;

  StudentImportData({
    required this.name,
    required this.phone,
    required this.address,
    required this.cityName,
    required this.dateBirth,
    required this.genderId,
    required this.religionId,
    required this.gradeId,
    this.typeBloodId,
    required this.tripType,
    required this.rowNumber,
  });

  factory StudentImportData.fromMap(Map<String, dynamic> map, int rowNumber) {
    return StudentImportData(
      name: map['name']?.toString() ?? '',
      phone: map['phone']?.toString() ?? '',
      address: map['address']?.toString() ?? '',
      cityName: map['cityName']?.toString() ?? '',
      dateBirth: map['dateBirth']?.toString() ?? '',
      genderId: map['genderId'] ?? 0,
      religionId: map['religionId'] ?? 0,
      gradeId: map['gradeId'] ?? 0,
      typeBloodId: map['typeBloodId'],
      tripType: map['tripType']?.toString() ?? '',
      rowNumber: rowNumber,
    );
  }

  /// Convert to Student entity for API submission
  Student toStudent() {
    return Student(
      name: name,
      phone: phone,
      address: address,
      cityName: cityName,
      dateBirth: dateBirth,
      genderId: genderId,
      religionId: religionId,
      gradeId: gradeId,
      typeBloodId: typeBloodId,
      tripType: tripType,
    );
  }
}
