import 'package:flutter/material.dart';
import '../constants/color_constants.dart';

/// AppTheme class responsible for managing the Busaty school bus management system's theme
/// Following Single Responsibility Principle by focusing only on theme management
class AppTheme {
  // Private constructor to prevent instantiation
  AppTheme._();

  /// Light theme configuration for the Busaty school bus management system
  static ThemeData get lightTheme {
    return ThemeData(
      useMaterial3: true,
      primaryColor: ColorConstants.primary,
      scaffoldBackgroundColor: ColorConstants.backgroundLightColor,
      colorScheme: ColorScheme(
        brightness: Brightness.light,
        primary: ColorConstants.primary,
        onPrimary: ColorConstants.white,
        secondary: ColorConstants.mainColor,
        onSecondary: ColorConstants.white,
        tertiary: ColorConstants.tertiaryColor,
        onTertiary: ColorConstants.text,
        error: ColorConstants.error,
        onError: ColorConstants.white,
        // Use surface instead of deprecated background
        surface: ColorConstants.fillFormField,
        onSurface: ColorConstants.text,
      ),
      appBarTheme: AppBarTheme(
        backgroundColor: ColorConstants.primary,
        foregroundColor: ColorConstants.white,
        elevation: 0,
        shadowColor: Colors.black.withAlpha(51), // 0.2 opacity = 51/255
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: ColorConstants.primary,
          foregroundColor: ColorConstants.white,
          elevation: 2,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        ),
      ),
      cardTheme: CardTheme(
        color: ColorConstants.surfaceLightColor,
        elevation: 2,
        margin: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 16.0),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      ),
      textTheme: TextTheme(
        headlineLarge: TextStyle(
          fontSize: 28,
          fontWeight: FontWeight.bold,
          color: ColorConstants.textPrimaryLightColor,
        ),
        headlineMedium: TextStyle(
          fontSize: 24,
          fontWeight: FontWeight.bold,
          color: ColorConstants.textPrimaryLightColor,
        ),
        titleLarge: TextStyle(
          fontSize: 20,
          fontWeight: FontWeight.bold,
          color: ColorConstants.textPrimaryLightColor,
        ),
        titleMedium: TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.w500,
          color: ColorConstants.textPrimaryLightColor,
        ),
        bodyLarge: TextStyle(
          fontSize: 16,
          color: ColorConstants.textPrimaryLightColor,
        ),
        bodyMedium: TextStyle(
          fontSize: 14,
          color: ColorConstants.textSecondaryLightColor,
        ),
      ),
    );
  }

  /// Dark theme configuration for the Busaty school bus management system
  static ThemeData get darkTheme {
    return ThemeData(
      useMaterial3: true,
      primaryColor: ColorConstants.primary,
      scaffoldBackgroundColor: ColorConstants.backgroundDarkColor,
      colorScheme: ColorScheme(
        brightness: Brightness.dark,
        primary: ColorConstants.primary,
        onPrimary: ColorConstants.white,
        secondary: ColorConstants.mainColor,
        onSecondary: ColorConstants.white,
        tertiary: ColorConstants.tertiaryColor,
        onTertiary: ColorConstants.white,
        error: ColorConstants.error,
        onError: ColorConstants.white,
        // Use surface instead of deprecated background
        surface: ColorConstants.text,
        onSurface: ColorConstants.white,
      ),
      appBarTheme: AppBarTheme(
        backgroundColor: ColorConstants.backgroundContainer,
        foregroundColor: ColorConstants.white,
        elevation: 0,
        shadowColor: Colors.black.withAlpha(102), // 0.4 opacity = 102/255
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: ColorConstants.mainColor,
          foregroundColor: ColorConstants.white,
          elevation: 2,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        ),
      ),
      cardTheme: CardTheme(
        color: ColorConstants.surfaceDarkColor,
        elevation: 2,
        margin: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 16.0),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      ),
      textTheme: TextTheme(
        headlineLarge: TextStyle(
          fontSize: 28,
          fontWeight: FontWeight.bold,
          color: ColorConstants.textPrimaryDarkColor,
        ),
        headlineMedium: TextStyle(
          fontSize: 24,
          fontWeight: FontWeight.bold,
          color: ColorConstants.textPrimaryDarkColor,
        ),
        titleLarge: TextStyle(
          fontSize: 20,
          fontWeight: FontWeight.bold,
          color: ColorConstants.textPrimaryDarkColor,
        ),
        titleMedium: TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.w500,
          color: ColorConstants.textPrimaryDarkColor,
        ),
        bodyLarge: TextStyle(
          fontSize: 16,
          color: ColorConstants.textPrimaryDarkColor,
        ),
        bodyMedium: TextStyle(
          fontSize: 14,
          color: ColorConstants.textSecondaryDarkColor,
        ),
      ),
    );
  }
}
