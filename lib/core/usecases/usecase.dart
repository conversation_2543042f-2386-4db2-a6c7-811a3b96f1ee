import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../errors/failures.dart';

/// Abstract UseCase class that defines the contract for all use cases
/// Following Interface Segregation Principle by creating a specific interface
/// for use cases that require parameters
abstract class UseCase<Type, Params> {
  Future<Either<Failure, Type>> call(Params params);
}

/// Abstract NoParamsUseCase class for use cases that don't require parameters
/// Following Interface Segregation Principle by creating a specific interface
/// for use cases that don't require parameters
abstract class NoParamsUseCase<Type> {
  Future<Either<Failure, Type>> call();
}

/// Abstract StreamUseCase class for stream-based use cases with parameters
/// Following Interface Segregation Principle by creating a specific interface
/// for stream-based use cases that require parameters
abstract class StreamUseCase<Type, Params> {
  Stream<Either<Failure, Type>> call(Params params);
}

/// Abstract StreamNoParamsUseCase class for stream-based use cases without parameters
/// Following Interface Segregation Principle by creating a specific interface
/// for stream-based use cases that don't require parameters
abstract class StreamNoParamsUseCase<Type> {
  Stream<Either<Failure, Type>> call();
}

/// NoParams class for use cases that don't require parameters
class NoParams extends Equatable {
  @override
  List<Object> get props => [];
}
