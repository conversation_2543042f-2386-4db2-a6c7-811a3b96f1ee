import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// Utility class for common functions
/// Following Single Responsibility Principle by focusing only on utility functions
class AppUtils {
  // Private constructor to prevent instantiation
  AppUtils._();
  
  /// Show a snackbar with the given message
  static void showSnackBar(String message, {bool isError = false}) {
    Get.snackbar(
      isError ? 'Error' : 'Message',
      message,
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: isError ? Colors.red : Colors.green,
      colorText: Colors.white,
      margin: const EdgeInsets.all(10),
      duration: const Duration(seconds: 3),
    );
  }
  
  /// Format a date to a readable string
  static String formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
  
  /// Format a time to a readable string
  static String formatTime(TimeOfDay time) {
    final hour = time.hour.toString().padLeft(2, '0');
    final minute = time.minute.toString().padLeft(2, '0');
    return '$hour:$minute';
  }
  
  /// Validate an email address
  static bool isValidEmail(String email) {
    return GetUtils.isEmail(email);
  }
  
  /// Validate a password (at least 8 characters, with at least one uppercase letter,
  /// one lowercase letter, one number, and one special character)
  static bool isValidPassword(String password) {
    if (password.length < 8) return false;
    
    bool hasUppercase = password.contains(RegExp(r'[A-Z]'));
    bool hasLowercase = password.contains(RegExp(r'[a-z]'));
    bool hasDigits = password.contains(RegExp(r'[0-9]'));
    bool hasSpecialCharacters = password.contains(RegExp(r'[!@#$%^&*(),.?":{}|<>]'));
    
    return hasUppercase && hasLowercase && hasDigits && hasSpecialCharacters;
  }
  
  /// Get a color based on the grade
  static Color getGradeColor(double grade) {
    if (grade >= 90) return Colors.green;
    if (grade >= 80) return Colors.lightGreen;
    if (grade >= 70) return Colors.yellow;
    if (grade >= 60) return Colors.orange;
    return Colors.red;
  }
}
