import 'dart:math' as math;
import 'package:flutter/material.dart';
import '../constants/color_constants.dart';

/// ColorUtils class for color-related utility functions
/// Following Single Responsibility Principle by focusing only on color utilities
class ColorUtils {
  // Private constructor to prevent instantiation
  ColorUtils._();

  /// Get status color based on status type
  static Color getStatusColor(String status) {
    return ColorConstants.statusColors[status.toLowerCase()] ??
           ColorConstants.neutralGreyColor;
  }

  /// Get a linear gradient from the primary colors
  static LinearGradient getPrimaryGradient({bool isHorizontal = true}) {
    return LinearGradient(
      colors: ColorConstants.primaryGradient,
      begin: isHorizontal ? Alignment.centerLeft : Alignment.topCenter,
      end: isHorizontal ? Alignment.centerRight : Alignment.bottomCenter,
    );
  }

  /// Get a linear gradient from the secondary colors
  static LinearGradient getSecondaryGradient({bool isHorizontal = true}) {
    return LinearGradient(
      colors: ColorConstants.secondaryGradient,
      begin: isHorizontal ? Alignment.centerLeft : Alignment.topCenter,
      end: isHorizontal ? Alignment.centerRight : Alignment.bottomCenter,
    );
  }

  /// Get a linear gradient from the tertiary colors
  static LinearGradient getTertiaryGradient({bool isHorizontal = true}) {
    return LinearGradient(
      colors: ColorConstants.tertiaryGradient,
      begin: isHorizontal ? Alignment.centerLeft : Alignment.topCenter,
      end: isHorizontal ? Alignment.centerRight : Alignment.bottomCenter,
    );
  }

  /// Get a background gradient based on the current theme mode
  static LinearGradient getBackgroundGradient(bool isDarkMode, {bool isHorizontal = true}) {
    return LinearGradient(
      colors: isDarkMode
          ? ColorConstants.backgroundGradientDark
          : ColorConstants.backgroundGradientLight,
      begin: isHorizontal ? Alignment.centerLeft : Alignment.topCenter,
      end: isHorizontal ? Alignment.centerRight : Alignment.bottomCenter,
    );
  }

  /// Get a color for status based on its current state
  static Color getStatusColor2(String status) {
    switch (status.toLowerCase()) {
      case 'active':
      case 'completed':
      case 'on_time':
        return ColorConstants.successColor;
      case 'pending':
      case 'delayed':
      case 'warning':
        return ColorConstants.warningColor;
      case 'cancelled':
      case 'error':
        return ColorConstants.errorColor;
      case 'in_progress':
      case 'in_transit':
      case 'info':
        return ColorConstants.infoColor;
      default:
        return ColorConstants.neutralGreyColor;
    }
  }

  /// Get a color that contrasts with the given background color
  static Color getContrastingTextColor(Color backgroundColor) {
    // Calculate the relative luminance of the background color
    // Formula: 0.299*R + 0.587*G + 0.114*B
    final double luminance = (0.299 * backgroundColor.red.toDouble() +
                             0.587 * backgroundColor.green.toDouble() +
                             0.114 * backgroundColor.blue.toDouble()) / 255;

    // If the background is dark, use white text; otherwise, use dark text
    return luminance > 0.5
        ? ColorConstants.textPrimaryLightColor
        : ColorConstants.textPrimaryDarkColor;
  }

  /// Calculate contrast ratio between two colors
  /// Returns a value between 1 and 21, where 1 is no contrast and 21 is max contrast
  /// WCAG 2.0 recommends a contrast ratio of at least 4.5:1 for normal text
  /// and 3:1 for large text
  static double getContrastRatio(Color foreground, Color background) {
    // Calculate relative luminance for both colors
    double l1 = _getRelativeLuminance(foreground);
    double l2 = _getRelativeLuminance(background);

    // Calculate contrast ratio
    return (math.max(l1, l2) + 0.05) / (math.min(l1, l2) + 0.05);
  }

  // Helper method to calculate relative luminance
  static double _getRelativeLuminance(Color color) {
    // Convert RGB to sRGB
    double r = _getLinearRGBComponent(color.red.toDouble() / 255);
    double g = _getLinearRGBComponent(color.green.toDouble() / 255);
    double b = _getLinearRGBComponent(color.blue.toDouble() / 255);

    // Calculate luminance
    return 0.2126 * r + 0.7152 * g + 0.0722 * b;
  }

  // Helper method for sRGB conversion
  static double _getLinearRGBComponent(double colorComponent) {
    if (colorComponent <= 0.03928) {
      return colorComponent / 12.92;
    } else {
      return math.pow((colorComponent + 0.055) / 1.055, 2.4).toDouble();
    }
  }

  /// Get a lighter version of the given color
  static Color getLighterColor(Color color, [double factor = 0.2]) {
    return Color.fromARGB(
      color.alpha.toInt(),
      _lightenValue(color.red.toInt(), factor),
      _lightenValue(color.green.toInt(), factor),
      _lightenValue(color.blue.toInt(), factor),
    );
  }

  /// Get a darker version of the given color
  static Color getDarkerColor(Color color, [double factor = 0.2]) {
    return Color.fromARGB(
      color.alpha.toInt(),
      _darkenValue(color.red.toInt(), factor),
      _darkenValue(color.green.toInt(), factor),
      _darkenValue(color.blue.toInt(), factor),
    );
  }

  // Helper method to lighten a color value
  static int _lightenValue(int value, double factor) {
    return min(255, value + ((255 - value) * factor).round());
  }

  // Helper method to darken a color value
  static int _darkenValue(int value, double factor) {
    return max(0, value - (value * factor).round());
  }

  // Helper method to get minimum value
  static int min(int a, int b) => a < b ? a : b;

  // Helper method to get maximum value
  static int max(int a, int b) => a > b ? a : b;
}
