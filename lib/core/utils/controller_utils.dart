import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../localization/language_controller.dart';
import '../../domain/usecases/auth/change_password.dart';
import '../../domain/usecases/auth/forgot_password.dart';
import '../../domain/usecases/auth/get_current_user.dart';
import '../../domain/usecases/auth/is_authenticated.dart';
import '../../domain/usecases/auth/login_user.dart';
import '../../domain/usecases/auth/logout_user.dart';
import '../../domain/usecases/auth/register_user.dart';
import '../../domain/usecases/auth/reset_password.dart';
import '../../domain/usecases/user/update_profile.dart';
import '../../domain/usecases/user/upload_profile_image.dart';
import '../../presentation/controllers/auth_controller.dart';
import '../../presentation/controllers/profile_controller.dart';
import '../../presentation/controllers/theme_controller.dart';

/// Utility functions for safely getting controllers
class ControllerUtils {
  /// Safely get the LanguageController
  /// This ensures the controller is registered before trying to use it
  static LanguageController getLanguageController() {
    if (!Get.isRegistered<LanguageController>()) {
      // If not registered, register it
      Get.put<LanguageController>(
        LanguageController(prefs: Get.find<SharedPreferences>()),
        permanent: true,
      );
    }

    // Now safely find the controller
    return Get.find<LanguageController>();
  }

  /// Safely get the ThemeController
  /// This ensures the controller is registered before trying to use it
  static ThemeController getThemeController() {
    if (!Get.isRegistered<ThemeController>()) {
      // If not registered, register it
      Get.put<ThemeController>(
        ThemeController(prefs: Get.find<SharedPreferences>()),
        permanent: true,
      );
    }

    // Now safely find the controller
    return Get.find<ThemeController>();
  }

  /// Safely get the AuthController
  /// This ensures the controller is registered before trying to use it
  static AuthController getAuthController() {
    if (!Get.isRegistered<AuthController>()) {
      // If not registered, register it
      Get.put<AuthController>(
        AuthController(
          loginUserUseCase: Get.find<LoginUser>(),
          logoutUserUseCase: Get.find<LogoutUser>(),
          registerUserUseCase: Get.find<RegisterUser>(),
          getCurrentUserUseCase: Get.find<GetCurrentUser>(),
          isAuthenticatedUseCase: Get.find<IsAuthenticated>(),
          changePasswordUseCase: Get.find<ChangePassword>(),
          forgotPasswordUseCase: Get.find<ForgotPassword>(),
          resetPasswordUseCase: Get.find<ResetPassword>(),
        ),
        permanent: true,
      );
    }

    // Now safely find the controller
    return Get.find<AuthController>();
  }

  /// Safely get the ProfileController
  /// This ensures the controller is registered before trying to use it
  static ProfileController getProfileController() {
    if (!Get.isRegistered<ProfileController>()) {
      // If not registered, register it
      Get.put<ProfileController>(
        ProfileController(
          updateProfileUseCase: Get.find<UpdateProfile>(),
          uploadProfileImageUseCase: Get.find<UploadProfileImage>(),
        ),
        permanent: true,
      );
    }

    // Now safely find the controller
    return Get.find<ProfileController>();
  }

  /// Generic method to safely get any controller
  /// This ensures the controller is registered before trying to use it
  static T getController<T>(T Function() factory) {
    if (!Get.isRegistered<T>()) {
      // If not registered, register it
      Get.put<T>(factory(), permanent: true);
    }

    // Now safely find the controller
    return Get.find<T>();
  }
}
