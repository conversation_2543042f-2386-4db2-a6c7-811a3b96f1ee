import 'package:flutter/foundation.dart';
import 'package:logger/logger.dart' as log_package;

/// LoggerService class for handling application logging
/// Following Single Responsibility Principle by focusing only on logging
class LoggerService {
  static final _logger = log_package.Logger(
    printer: log_package.PrettyPrinter(
      methodCount: 0,
      errorMethodCount: 8,
      lineLength: 120,
      colors: true,
      printEmojis: true,
      printTime: true,
    ),
  );

  /// Log debug message
  static void debug(String message, {dynamic data, dynamic error, StackTrace? stackTrace}) {
    if (kDebugMode) {
      _logger.d(message, error: error, stackTrace: stackTrace);
      if (data != null) {
        _logger.d('Data: $data');
      }
    }
  }

  /// Log info message
  static void info(String message, {dynamic data, dynamic error, StackTrace? stackTrace}) {
    if (kDebugMode) {
      _logger.i(message, error: error, stackTrace: stackTrace);
      if (data != null) {
        _logger.i('Data: $data');
      }
    }
  }

  /// Log warning message
  static void warning(String message, {dynamic data, dynamic error, StackTrace? stackTrace}) {
    if (kDebugMode) {
      _logger.w(message, error: error, stackTrace: stackTrace);
      if (data != null) {
        _logger.w('Data: $data');
      }
    }
  }

  /// Log error message
  static void error(String message, {dynamic data, dynamic error, StackTrace? stackTrace}) {
    if (kDebugMode) {
      _logger.e(message, error: error, stackTrace: stackTrace);
      if (data != null) {
        _logger.e('Data: $data');
      }
    }
  }

  /// Log success message
  static void success(String message, {dynamic data, dynamic error, StackTrace? stackTrace}) {
    if (kDebugMode) {
      _logger.i('✅ $message', error: error, stackTrace: stackTrace);
      if (data != null) {
        _logger.i('Data: $data');
      }
    }
  }

  /// Log request message
  static void request(String message, {dynamic data, dynamic headers, StackTrace? stackTrace}) {
    if (kDebugMode) {
      _logger.i('🌐 $message', stackTrace: stackTrace);
      if (headers != null) {
        _logger.i('Headers: $headers');
      }
      if (data != null) {
        _logger.i('Data: $data');
      }
    }
  }

  /// Log firebase message
  static void firebase(String message, {dynamic data, dynamic error, StackTrace? stackTrace}) {
    if (kDebugMode) {
      _logger.i('🔥 $message', error: error, stackTrace: stackTrace);
      if (data != null) {
        _logger.i('Data: $data');
      }
    }
  }
}
