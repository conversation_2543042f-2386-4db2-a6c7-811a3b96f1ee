import 'dart:typed_data';
import 'dart:html' as html;

/// Web download utilities for file downloads
class WebDownloadUtils {
  /// Download file in web browser
  static void downloadFile(Uint8List bytes, String fileName, String mimeType) {
    final blob = html.Blob([bytes], mimeType);
    final url = html.Url.createObjectUrlFromBlob(blob);
    
    final anchor = html.AnchorElement(href: url)
      ..setAttribute('download', fileName)
      ..click();
    
    html.Url.revokeObjectUrl(url);
  }

  /// Download Excel file
  static void downloadExcelFile(Uint8List bytes, String fileName) {
    downloadFile(
      bytes, 
      fileName.endsWith('.xlsx') ? fileName : '$fileName.xlsx',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    );
  }
}
