import '../../core/errors/exceptions.dart';
import '../../core/network/api_service.dart';
import '../../core/utils/logger.dart';
import '../../domain/entities/ad.dart';
import '../models/ad_model.dart';
import 'ad_remote_data_source.dart';

/// AdRemoteDataSourceImpl class for implementing AdRemoteDataSource
/// Following Single Responsibility Principle by focusing only on remote data source
class AdRemoteDataSourceImpl implements AdRemoteDataSource {
  final ApiService apiService;

  AdRemoteDataSourceImpl({
    required this.apiService,
  });

  @override
  Future<List<Ad>> getAds() async {
    try {
      LoggerService.info('Getting ads from API');
      
      // Make API request
      final response = await apiService.get('/ads');
      
      // Check response
      if (response.statusCode != 200) {
        throw ServerException(
          message: response.data['message'] ?? 'Failed to get ads',
        );
      }
      
      // Parse response
      final List<dynamic> adsData = response.data['data'];
      
      // Convert to models
      return adsData
          .map((adData) => AdModel.fromJson(adData))
          .toList();
    } catch (e) {
      LoggerService.error('Error getting ads from API', error: e);
      rethrow;
    }
  }
}
