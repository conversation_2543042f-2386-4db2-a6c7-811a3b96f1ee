import '../../core/network/api_service.dart';
import '../../domain/entities/dashboard_stats.dart';
import '../../domain/entities/school_stats.dart';
import '../../domain/entities/trip.dart';
import '../models/dashboard_stats_model.dart';
import '../models/school_stats_model.dart';
import '../models/trip_model.dart';
import 'dashboard_remote_data_source.dart';

/// DashboardRemoteDataSourceImpl implementation
/// Following Single Responsibility Principle by focusing only on remote data operations
class DashboardRemoteDataSourceImpl implements DashboardRemoteDataSource {
  final ApiService apiService;

  DashboardRemoteDataSourceImpl({
    required this.apiService,
  });

  @override
  Future<DashboardStats> getDashboardStats() async {
    // For now, return mock data
    // In a real implementation, this would call the API service
    await Future.delayed(const Duration(seconds: 1));
    
    return DashboardStatsModel(
      totalStudents: 1250,
      totalBuses: 45,
      totalDrivers: 40,
      totalSupervisors: 30,
      totalTrips: 120,
      completedTrips: 80,
      cancelledTrips: 5,
      inProgressTrips: 10,
      upcomingTrips: 25,
      attendanceRate: 92.5,
      onTimeRate: 88.7,
    );
  }

  @override
  Future<List<SchoolStats>> getSchoolStats() async {
    // For now, return mock data
    // In a real implementation, this would call the API service
    await Future.delayed(const Duration(seconds: 1));
    
    return [
      SchoolStatsModel(
        id: '1',
        name: 'مدرسة الأمل',
        studentCount: 250,
        busCount: 8,
        tripCount: 24,
        supervisorCount: 6,
        driverCount: 8,
        lastTripDate: DateTime.now().subtract(const Duration(days: 1)),
      ),
      SchoolStatsModel(
        id: '2',
        name: 'مدرسة النور',
        studentCount: 180,
        busCount: 6,
        tripCount: 18,
        supervisorCount: 4,
        driverCount: 6,
        lastTripDate: DateTime.now().subtract(const Duration(days: 2)),
      ),
      SchoolStatsModel(
        id: '3',
        name: 'مدرسة المستقبل',
        studentCount: 320,
        busCount: 12,
        tripCount: 36,
        supervisorCount: 8,
        driverCount: 12,
        lastTripDate: DateTime.now().subtract(const Duration(days: 1)),
      ),
      SchoolStatsModel(
        id: '4',
        name: 'مدرسة الرياض',
        studentCount: 210,
        busCount: 7,
        tripCount: 21,
        supervisorCount: 5,
        driverCount: 7,
        lastTripDate: DateTime.now().subtract(const Duration(days: 3)),
      ),
      SchoolStatsModel(
        id: '5',
        name: 'مدرسة الفيصل',
        studentCount: 290,
        busCount: 10,
        tripCount: 30,
        supervisorCount: 7,
        driverCount: 10,
        lastTripDate: DateTime.now().subtract(const Duration(days: 2)),
      ),
    ];
  }

  @override
  Future<List<Trip>> getRecentTrips() async {
    // For now, return mock data
    // In a real implementation, this would call the API service
    await Future.delayed(const Duration(seconds: 1));
    
    return [
      TripModel(
        id: '1',
        name: 'رحلة الصباح - مدرسة الأمل',
        schoolId: '1',
        schoolName: 'مدرسة الأمل',
        busId: '1',
        busNumber: 'B-001',
        driverId: '1',
        driverName: 'أحمد محمد',
        supervisorId: '1',
        supervisorName: 'سارة أحمد',
        startTime: DateTime.now().subtract(const Duration(days: 1, hours: 8)),
        endTime: DateTime.now().subtract(const Duration(days: 1, hours: 7)),
        status: 'completed',
        studentIds: ['1', '2', '3', '4', '5'],
        attendedStudentIds: ['1', '2', '3', '5'],
        startLocation: 'حي النزهة',
        endLocation: 'مدرسة الأمل',
        distance: 12.5,
        duration: 45,
        stops: [],
        events: [],
      ),
      TripModel(
        id: '2',
        name: 'رحلة الظهر - مدرسة الأمل',
        schoolId: '1',
        schoolName: 'مدرسة الأمل',
        busId: '1',
        busNumber: 'B-001',
        driverId: '1',
        driverName: 'أحمد محمد',
        supervisorId: '1',
        supervisorName: 'سارة أحمد',
        startTime: DateTime.now().subtract(const Duration(days: 1, hours: 2)),
        endTime: DateTime.now().subtract(const Duration(days: 1, hours: 1)),
        status: 'completed',
        studentIds: ['1', '2', '3', '4', '5'],
        attendedStudentIds: ['1', '2', '3', '4', '5'],
        startLocation: 'مدرسة الأمل',
        endLocation: 'حي النزهة',
        distance: 12.5,
        duration: 50,
        stops: [],
        events: [],
      ),
      TripModel(
        id: '3',
        name: 'رحلة الصباح - مدرسة النور',
        schoolId: '2',
        schoolName: 'مدرسة النور',
        busId: '2',
        busNumber: 'B-002',
        driverId: '2',
        driverName: 'محمد علي',
        supervisorId: '2',
        supervisorName: 'فاطمة محمد',
        startTime: DateTime.now().subtract(const Duration(days: 1, hours: 8)),
        endTime: DateTime.now().subtract(const Duration(days: 1, hours: 7)),
        status: 'completed',
        studentIds: ['6', '7', '8', '9', '10'],
        attendedStudentIds: ['6', '7', '9', '10'],
        startLocation: 'حي العليا',
        endLocation: 'مدرسة النور',
        distance: 10.2,
        duration: 40,
        stops: [],
        events: [],
      ),
      TripModel(
        id: '4',
        name: 'رحلة الظهر - مدرسة النور',
        schoolId: '2',
        schoolName: 'مدرسة النور',
        busId: '2',
        busNumber: 'B-002',
        driverId: '2',
        driverName: 'محمد علي',
        supervisorId: '2',
        supervisorName: 'فاطمة محمد',
        startTime: DateTime.now().subtract(const Duration(days: 1, hours: 2)),
        endTime: DateTime.now().subtract(const Duration(days: 1, hours: 1)),
        status: 'cancelled',
        studentIds: ['6', '7', '8', '9', '10'],
        attendedStudentIds: [],
        startLocation: 'مدرسة النور',
        endLocation: 'حي العليا',
        distance: 10.2,
        duration: 40,
        stops: [],
        events: [],
      ),
      TripModel(
        id: '5',
        name: 'رحلة الصباح - مدرسة المستقبل',
        schoolId: '3',
        schoolName: 'مدرسة المستقبل',
        busId: '3',
        busNumber: 'B-003',
        driverId: '3',
        driverName: 'خالد عبدالله',
        supervisorId: '3',
        supervisorName: 'نورة خالد',
        startTime: DateTime.now().subtract(const Duration(days: 1, hours: 8)),
        endTime: DateTime.now().subtract(const Duration(days: 1, hours: 7)),
        status: 'completed',
        studentIds: ['11', '12', '13', '14', '15'],
        attendedStudentIds: ['11', '12', '13', '14', '15'],
        startLocation: 'حي الملز',
        endLocation: 'مدرسة المستقبل',
        distance: 15.8,
        duration: 55,
        stops: [],
        events: [],
      ),
    ];
  }

  @override
  Future<List<Trip>> getUpcomingTrips() async {
    // For now, return mock data
    // In a real implementation, this would call the API service
    await Future.delayed(const Duration(seconds: 1));
    
    return [
      TripModel(
        id: '6',
        name: 'رحلة الصباح - مدرسة الأمل',
        schoolId: '1',
        schoolName: 'مدرسة الأمل',
        busId: '1',
        busNumber: 'B-001',
        driverId: '1',
        driverName: 'أحمد محمد',
        supervisorId: '1',
        supervisorName: 'سارة أحمد',
        startTime: DateTime.now().add(const Duration(hours: 16)),
        status: 'scheduled',
        studentIds: ['1', '2', '3', '4', '5'],
        attendedStudentIds: [],
        startLocation: 'حي النزهة',
        endLocation: 'مدرسة الأمل',
        distance: 12.5,
        duration: 45,
        stops: [],
        events: [],
      ),
      TripModel(
        id: '7',
        name: 'رحلة الظهر - مدرسة الأمل',
        schoolId: '1',
        schoolName: 'مدرسة الأمل',
        busId: '1',
        busNumber: 'B-001',
        driverId: '1',
        driverName: 'أحمد محمد',
        supervisorId: '1',
        supervisorName: 'سارة أحمد',
        startTime: DateTime.now().add(const Duration(hours: 22)),
        status: 'scheduled',
        studentIds: ['1', '2', '3', '4', '5'],
        attendedStudentIds: [],
        startLocation: 'مدرسة الأمل',
        endLocation: 'حي النزهة',
        distance: 12.5,
        duration: 50,
        stops: [],
        events: [],
      ),
      TripModel(
        id: '8',
        name: 'رحلة الصباح - مدرسة النور',
        schoolId: '2',
        schoolName: 'مدرسة النور',
        busId: '2',
        busNumber: 'B-002',
        driverId: '2',
        driverName: 'محمد علي',
        supervisorId: '2',
        supervisorName: 'فاطمة محمد',
        startTime: DateTime.now().add(const Duration(hours: 16)),
        status: 'scheduled',
        studentIds: ['6', '7', '8', '9', '10'],
        attendedStudentIds: [],
        startLocation: 'حي العليا',
        endLocation: 'مدرسة النور',
        distance: 10.2,
        duration: 40,
        stops: [],
        events: [],
      ),
      TripModel(
        id: '9',
        name: 'رحلة الظهر - مدرسة النور',
        schoolId: '2',
        schoolName: 'مدرسة النور',
        busId: '2',
        busNumber: 'B-002',
        driverId: '2',
        driverName: 'محمد علي',
        supervisorId: '2',
        supervisorName: 'فاطمة محمد',
        startTime: DateTime.now().add(const Duration(hours: 22)),
        status: 'scheduled',
        studentIds: ['6', '7', '8', '9', '10'],
        attendedStudentIds: [],
        startLocation: 'مدرسة النور',
        endLocation: 'حي العليا',
        distance: 10.2,
        duration: 40,
        stops: [],
        events: [],
      ),
      TripModel(
        id: '10',
        name: 'رحلة الصباح - مدرسة المستقبل',
        schoolId: '3',
        schoolName: 'مدرسة المستقبل',
        busId: '3',
        busNumber: 'B-003',
        driverId: '3',
        driverName: 'خالد عبدالله',
        supervisorId: '3',
        supervisorName: 'نورة خالد',
        startTime: DateTime.now().add(const Duration(hours: 16)),
        status: 'scheduled',
        studentIds: ['11', '12', '13', '14', '15'],
        attendedStudentIds: [],
        startLocation: 'حي الملز',
        endLocation: 'مدرسة المستقبل',
        distance: 15.8,
        duration: 55,
        stops: [],
        events: [],
      ),
    ];
  }
}
