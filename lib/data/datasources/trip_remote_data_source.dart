import '../../domain/entities/trip.dart';

/// TripRemoteDataSource interface
/// Following Dependency Inversion Principle
abstract class TripRemoteDataSource {
  /// Get current trip from remote API
  Future<Trip?> getCurrentTrip();
  
  /// Get trip by id from remote API
  Future<Trip> getTripById(String id);
  
  /// Get upcoming trips from remote API
  Future<List<Trip>> getUpcomingTrips();
  
  /// Get recent trips from remote API
  Future<List<Trip>> getRecentTrips();
}
