import '../../core/errors/exceptions.dart';
import '../../core/network/api_service.dart';
import '../../core/utils/logger.dart';
import '../../domain/entities/trip.dart';
import '../models/trip_model.dart';
import 'trip_remote_data_source.dart';

/// TripRemoteDataSourceImpl class for implementing TripRemoteDataSource
/// Following Single Responsibility Principle by focusing only on remote data source
class TripRemoteDataSourceImpl implements TripRemoteDataSource {
  final ApiService apiService;

  TripRemoteDataSourceImpl({
    required this.apiService,
  });

  @override
  Future<Trip?> getCurrentTrip() async {
    try {
      LoggerService.info('Getting current trip from API');
      
      // Make API request
      final response = await apiService.get('/trips/current');
      
      // Check response
      if (response.statusCode != 200) {
        throw ServerException(
          message: response.data['message'] ?? 'Failed to get current trip',
        );
      }
      
      // Parse response
      final tripData = response.data['data'];
      
      // If no current trip
      if (tripData == null) {
        return null;
      }
      
      // Convert to model
      return TripModel.fromJson(tripData);
    } catch (e) {
      LoggerService.error('Error getting current trip from API', error: e);
      rethrow;
    }
  }

  @override
  Future<Trip> getTripById(String id) async {
    try {
      LoggerService.info('Getting trip by id from API', data: {'id': id});
      
      // Make API request
      final response = await apiService.get('/trips/$id');
      
      // Check response
      if (response.statusCode != 200) {
        throw ServerException(
          message: response.data['message'] ?? 'Failed to get trip',
        );
      }
      
      // Parse response
      final tripData = response.data['data'];
      
      // Convert to model
      return TripModel.fromJson(tripData);
    } catch (e) {
      LoggerService.error('Error getting trip by id from API', error: e);
      rethrow;
    }
  }

  @override
  Future<List<Trip>> getUpcomingTrips() async {
    try {
      LoggerService.info('Getting upcoming trips from API');
      
      // Make API request
      final response = await apiService.get('/trips/upcoming');
      
      // Check response
      if (response.statusCode != 200) {
        throw ServerException(
          message: response.data['message'] ?? 'Failed to get upcoming trips',
        );
      }
      
      // Parse response
      final List<dynamic> tripsData = response.data['data'];
      
      // Convert to models
      return tripsData
          .map((tripData) => TripModel.fromJson(tripData))
          .toList();
    } catch (e) {
      LoggerService.error('Error getting upcoming trips from API', error: e);
      rethrow;
    }
  }

  @override
  Future<List<Trip>> getRecentTrips() async {
    try {
      LoggerService.info('Getting recent trips from API');
      
      // Make API request
      final response = await apiService.get('/trips/recent');
      
      // Check response
      if (response.statusCode != 200) {
        throw ServerException(
          message: response.data['message'] ?? 'Failed to get recent trips',
        );
      }
      
      // Parse response
      final List<dynamic> tripsData = response.data['data'];
      
      // Convert to models
      return tripsData
          .map((tripData) => TripModel.fromJson(tripData))
          .toList();
    } catch (e) {
      LoggerService.error('Error getting recent trips from API', error: e);
      rethrow;
    }
  }
}
