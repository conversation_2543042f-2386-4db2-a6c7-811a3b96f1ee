import '../models/user_model.dart';

/// UserLocalDataSource interface
/// Following Interface Segregation Principle by creating a specific interface for local data operations
abstract class UserLocalDataSource {
  /// Get cached user
  Future<UserModel> getCachedUser();

  /// Cache user
  Future<void> cacheUser(UserModel user);

  /// Clear cached user
  Future<bool> clearCachedUser();

  /// Check if user is authenticated
  Future<bool> isAuthenticated();

  /// Cache authentication token
  Future<void> cacheToken(String token);

  /// Get cached token
  Future<String?> getCachedToken();

  /// Clear cached token
  Future<bool> clearCachedToken();
}
