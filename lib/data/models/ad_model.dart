import '../../domain/entities/ad.dart';

/// AdModel class for Ad entity
/// Following Single Responsibility Principle by focusing only on ad data
class AdModel extends Ad {
  AdModel({
    required int id,
    required String title,
    required String description,
    required String imageUrl,
    required String link,
    required DateTime startDate,
    required DateTime endDate,
    required bool isActive,
  }) : super(
          id: id,
          title: title,
          description: description,
          imageUrl: imageUrl,
          link: link,
          startDate: startDate,
          endDate: endDate,
          isActive: isActive,
        );

  /// Create AdModel from JSON
  factory AdModel.fromJson(Map<String, dynamic> json) {
    return AdModel(
      id: json['id'],
      title: json['title'],
      description: json['description'],
      imageUrl: json['image_url'],
      link: json['link'] ?? '',
      startDate: DateTime.parse(json['start_date']),
      endDate: DateTime.parse(json['end_date']),
      isActive: json['is_active'] == 1 || json['is_active'] == true,
    );
  }

  /// Convert AdModel to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'image_url': imageUrl,
      'link': link,
      'start_date': startDate.toIso8601String(),
      'end_date': endDate.toIso8601String(),
      'is_active': isActive ? 1 : 0,
    };
  }
}
