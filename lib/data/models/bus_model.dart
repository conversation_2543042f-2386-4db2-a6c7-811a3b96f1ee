import '../../domain/entities/bus.dart';

/// BusModel model
class BusModel extends Bus {
  const BusModel({
    super.id,
    super.name,
    super.carNumber,
    super.notes,
    super.createdAt,
    super.updatedAt,
  });

  factory BusModel.fromJson(Map<String, dynamic> json) {
    return BusModel(
      id: json['id'],
      name: json['name'],
      carNumber: json['car_number'],
      notes: json['notes'],
      createdAt: json['created_at'],
      updatedAt: json['updated_at'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'car_number': carNumber,
      'notes': notes,
      'created_at': createdAt,
      'updated_at': updatedAt,
    };
  }
}
