import '../../domain/entities/student.dart';
import '../../core/utils/logger.dart';
import 'bus_model.dart';
import 'grade_model.dart';
import 'parent_model.dart';
import 'type_blood_model.dart';

/// StudentsResponse model
class StudentsResponse {
  final bool status;
  final String message;
  final StudentsData data;

  StudentsResponse({
    required this.status,
    required this.message,
    required this.data,
  });

  factory StudentsResponse.fromJson(Map<String, dynamic> json) {
    // تسجيل البيانات للتشخيص
    LoggerService.debug('StudentsResponse.fromJson input', data: json);

    // تعامل مع الاستجابة بنفس طريقة المشروع القديم
    final bool status = json['errors'] == false; // في API، errors: false تعني نجاح العملية
    LoggerService.debug('Status based on errors', data: status);

    // تحويل الرسالة إلى نص بشكل آمن
    String safeMessage = '';
    if (json['message'] != null) {
      LoggerService.debug('Message type', data: json['message'].runtimeType.toString());
      if (json['message'] is String) {
        safeMessage = json['message'];
      } else {
        safeMessage = json['message'].toString();
      }
    }
    LoggerService.debug('Safe message', data: safeMessage);

    // إنشاء كائن StudentsData
    final studentsData = StudentsData.fromJson(json);

    return StudentsResponse(
      status: status,
      message: safeMessage,
      data: studentsData,
    );
  }
}

/// StudentsData model
class StudentsData {
  final List<StudentModel> students;
  final int currentPage;
  final int lastPage;
  final int total;

  StudentsData({
    required this.students,
    required this.currentPage,
    required this.lastPage,
    required this.total,
  });

  factory StudentsData.fromJson(Map<String, dynamic> json) {
    // تسجيل البيانات للتشخيص
    LoggerService.debug('StudentsData.fromJson input', data: json);

    // تعامل مع الاستجابة بنفس طريقة المشروع القديم
    final studentsData = json['data']?['students'] ?? {};
    LoggerService.debug('Students data', data: studentsData);

    // تحويل البيانات بشكل آمن
    List<dynamic> studentsJson = [];
    if (studentsData['data'] != null) {
      LoggerService.debug('Students data type', data: studentsData['data'].runtimeType.toString());
      if (studentsData['data'] is List) {
        studentsJson = studentsData['data'] as List<dynamic>;
        LoggerService.debug('Students JSON list length', data: studentsJson.length);
      }
    }

    // تحويل معلومات الصفحات بشكل آمن
    int safeGetInt(dynamic value, int defaultValue) {
      if (value == null) return defaultValue;
      if (value is int) return value;
      if (value is String) {
        final parsed = int.tryParse(value);
        if (parsed != null) return parsed;
      }
      return defaultValue;
    }

    final currentPage = safeGetInt(studentsData['current_page'], 1);
    final lastPage = safeGetInt(studentsData['last_page'], 1);
    final total = safeGetInt(studentsData['total'], 0);

    LoggerService.debug('Pagination info', data: {
      'currentPage': currentPage,
      'lastPage': lastPage,
      'total': total,
    });

    final studentsList = studentsJson
        .map((student) {
          if (student is Map<String, dynamic>) {
            try {
              return StudentModel.fromJson(student);
            } catch (e) {
              LoggerService.error('Error parsing student', error: e);
              return StudentModel(id: '0', name: 'Error: ${e.toString()}');
            }
          } else {
            LoggerService.warning('Student is not a Map', data: student);
            return StudentModel(id: '0', name: 'Unknown');
          }
        })
        .toList();

    LoggerService.debug('Parsed students count', data: studentsList.length);

    return StudentsData(
      students: studentsList,
      currentPage: currentPage,
      lastPage: lastPage,
      total: total,
    );
  }
}

/// StudentModel model
class StudentModel extends Student {
  const StudentModel({
    super.id,
    super.name,
    super.phone,
    super.cityName,
    super.address,
    super.logoPath,
    super.updatedAt,
    super.createdAt,
    super.deletedAt,
    super.logo,
    super.longitude,
    super.latitude,
    super.attendantAdminsId,
    super.attendantDriverId,
    super.busId,
    super.classroomId,
    super.dateBirth,
    super.genderId,
    super.gradeId,
    super.parentKey,
    super.parentSecret,
    super.religionId,
    super.schoolId,
    super.tripType,
    super.typeBloodId,
    super.schools,
    super.gender,
    super.religion,
    super.typeBlood,
    super.bus,
    super.classroom,
    super.attendantAdmins,
    super.attendantDriver,
    super.attendance,
    super.grade,
    super.parents,
  });

  factory StudentModel.fromJson(Map<String, dynamic> json) {
    // تحويل البيانات بشكل آمن مع التحقق من النوع
    String? safeGetString(dynamic value) {
      if (value == null) return null;
      if (value is String) return value;
      return value.toString();
    }

    int? safeGetInt(dynamic value) {
      if (value == null) return null;
      if (value is int) return value;
      if (value is String) return int.tryParse(value);
      return null;
    }

    return StudentModel(
      id: safeGetString(json['id']),
      name: safeGetString(json['name']),
      phone: safeGetString(json['phone']),
      cityName: safeGetString(json['city_name']),
      address: safeGetString(json['address']),
      logoPath: safeGetString(json['logo_path']),
      updatedAt: safeGetString(json['updated_at']),
      createdAt: safeGetString(json['created_at']),
      deletedAt: safeGetString(json['deleted_at']),
      logo: safeGetString(json['logo']),
      longitude: safeGetString(json['longitude']),
      latitude: safeGetString(json['latitude']),
      attendantAdminsId: safeGetString(json['attendant_admins_id']),
      attendantDriverId: safeGetString(json['attendant_driver_id']),
      busId: safeGetInt(json['bus_id']),
      classroomId: safeGetInt(json['classroom_id']),
      dateBirth: safeGetString(json['Date_Birth']),
      genderId: safeGetInt(json['gender_id']),
      gradeId: safeGetInt(json['grade_id']),
      parentKey: safeGetString(json['parent_key']),
      parentSecret: safeGetString(json['parent_secret']),
      religionId: safeGetInt(json['religion_id']),
      schoolId: safeGetInt(json['school_id']),
      tripType: safeGetString(json['trip_type']),
      typeBloodId: safeGetInt(json['type__blood_id']),
      schools: safeGetString(json['schools']),
      gender: safeGetString(json['gender']),
      religion: safeGetString(json['religion']),
      typeBlood: json['type_blood'] != null && json['type_blood'] is Map<String, dynamic>
          ? TypeBloodModel.fromJson(json['type_blood'] as Map<String, dynamic>)
          : null,
      bus: json['bus'] != null && json['bus'] is Map<String, dynamic>
          ? BusModel.fromJson(json['bus'] as Map<String, dynamic>)
          : null,
      classroom: safeGetString(json['classroom']),
      attendantAdmins: safeGetString(json['attendant_admins']),
      attendantDriver: safeGetString(json['attendant_driver']),
      attendance: safeGetString(json['attendance']),
      grade: json['grade'] != null && json['grade'] is Map<String, dynamic>
          ? GradeModel.fromJson(json['grade'] as Map<String, dynamic>)
          : null,
      parents: json['parent'] != null && json['parent'] is List
          ? (json['parent'] as List)
              .map((parent) => parent is Map<String, dynamic>
                  ? ParentModel.fromJson(parent)
                  : null)
              .whereType<ParentModel>()
              .toList()
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'phone': phone,
      'city_name': cityName,
      'address': address,
      'logo_path': logoPath,
      'updated_at': updatedAt,
      'created_at': createdAt,
      'deleted_at': deletedAt,
      'logo': logo,
      'longitude': longitude,
      'latitude': latitude,
      'attendant_admins_id': attendantAdminsId,
      'attendant_driver_id': attendantDriverId,
      'bus_id': busId,
      'classroom_id': classroomId,
      'Date_Birth': dateBirth,
      'gender_id': genderId,
      'grade_id': gradeId,
      'parent_key': parentKey,
      'parent_secret': parentSecret,
      'religion_id': religionId,
      'school_id': schoolId,
      'trip_type': tripType,
      'type__blood_id': typeBloodId,
      'schools': schools,
      'gender': gender,
      'religion': religion,
      'type_blood': (typeBlood as TypeBloodModel?)?.toJson(),
      'bus': (bus as BusModel?)?.toJson(),
      'classroom': classroom,
      'attendant_admins': attendantAdmins,
      'attendant_driver': attendantDriver,
      'attendance': attendance,
      'grade': (grade as GradeModel?)?.toJson(),
      'parent': parents?.map((parent) => (parent as ParentModel).toJson()).toList(),
    };
  }
}
