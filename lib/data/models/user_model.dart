import '../../domain/entities/user.dart';

/// UserModel class that extends the User entity
/// Following Liskov Substitution Principle by ensuring UserModel can be used wherever User is expected
class UserModel extends User {
  const UserModel({
    required super.id,
    required super.name,
    required super.email,
    super.phone,
    super.type,
    super.role,
    super.address,
    super.cityName,
    super.logo,
    super.logoPath,
    super.imageUrl,
    super.token,
    super.latitude,
    super.longitude,
    super.schoolId,
    super.isVerified,
    super.createdAt,
    super.updatedAt,
  });

  /// Create a UserModel from a JSON map
  factory UserModel.fromJson(Map<String, dynamic> json) {
    return UserModel(
      // Convert ID to string regardless of whether it's an int or string
      id: json['id'] != null ? json['id'].toString() : '',
      name: json['name'] ?? '',
      email: json['email'] ?? '',
      phone: json['phone'],
      // Handle typeAuth field from SchoolX API
      type: json['type'] ?? json['typeAuth'],
      role: json['role'],
      address: json['address'],
      cityName: json['city_name'],
      logo: json['logo'],
      logoPath: json['logo_path'],
      imageUrl: json['image_url'] ?? json['avatar'] ?? json['logo_path'],
      // Handle token field from SchoolX API response
      token: json['token'],
      latitude:
          json['latitude'] != null
              ? double.parse(json['latitude'].toString())
              : null,
      longitude:
          json['longitude'] != null
              ? double.parse(json['longitude'].toString())
              : null,
      schoolId:
          json['school_id'] != null
              ? int.parse(json['school_id'].toString())
              : null,
      isVerified:
          json['email_verified_at'] != null
              ? json['email_verified_at'] == 1 ||
                  json['email_verified_at'] == true
              : json['is_verified'] != null
              ? json['is_verified'] == 1 || json['is_verified'] == true
              : null,
      createdAt:
          json['created_at'] != null
              ? DateTime.parse(json['created_at'])
              : null,
      updatedAt:
          json['updated_at'] != null
              ? DateTime.parse(json['updated_at'])
              : null,
    );
  }

  /// Convert UserModel to a JSON map
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {'id': id, 'name': name, 'email': email};

    // Add optional fields only if they are not null
    if (phone != null) data['phone'] = phone;
    if (type != null) {
      data['type'] = type;
      data['typeAuth'] = type; // Include both formats for compatibility
    }
    if (role != null) data['role'] = role;
    if (address != null) data['address'] = address;
    if (cityName != null) data['city_name'] = cityName;
    if (logo != null) data['logo'] = logo;
    if (logoPath != null) data['logo_path'] = logoPath;
    if (imageUrl != null) data['image_url'] = imageUrl;
    if (token != null) data['token'] = token;
    if (latitude != null) data['latitude'] = latitude.toString();
    if (longitude != null) data['longitude'] = longitude.toString();
    if (schoolId != null) data['school_id'] = schoolId.toString();
    if (isVerified != null) {
      data['is_verified'] = isVerified == true ? 1 : 0;
      data['email_verified_at'] =
          isVerified == true ? 1 : 0; // Include both formats for compatibility
    }
    if (createdAt != null) data['created_at'] = createdAt!.toIso8601String();
    if (updatedAt != null) data['updated_at'] = updatedAt!.toIso8601String();

    return data;
  }

  /// Create an empty UserModel
  factory UserModel.empty() {
    return const UserModel(id: '', name: '', email: '');
  }

  /// Create a copy of this UserModel with the given fields replaced with the new values
  @override
  UserModel copyWith({
    String? id,
    String? name,
    String? email,
    String? phone,
    String? type,
    String? role,
    String? address,
    String? cityName,
    String? logo,
    String? logoPath,
    String? imageUrl,
    String? token,
    double? latitude,
    double? longitude,
    int? schoolId,
    bool? isVerified,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return UserModel(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      type: type ?? this.type,
      role: role ?? this.role,
      address: address ?? this.address,
      cityName: cityName ?? this.cityName,
      logo: logo ?? this.logo,
      logoPath: logoPath ?? this.logoPath,
      imageUrl: imageUrl ?? this.imageUrl,
      token: token ?? this.token,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      schoolId: schoolId ?? this.schoolId,
      isVerified: isVerified ?? this.isVerified,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}
