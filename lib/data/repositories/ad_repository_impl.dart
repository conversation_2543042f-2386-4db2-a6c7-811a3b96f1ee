import 'package:dartz/dartz.dart';

import '../../core/errors/exceptions.dart';
import '../../core/errors/failures.dart';
import '../../core/utils/logger.dart';
import '../../domain/entities/ad.dart';
import '../../domain/repositories/ad_repository.dart';
import '../datasources/ad_remote_data_source.dart';
import '../models/ad_model.dart';

/// AdRepositoryImpl class for implementing AdRepository
/// Following Repository Pattern and Dependency Inversion Principle
class AdRepositoryImpl implements AdRepository {
  final AdRemoteDataSource remoteDataSource;

  AdRepositoryImpl({required this.remoteDataSource});

  @override
  Future<Either<Failure, List<Ad>>> getAds() async {
    try {
      LoggerService.info('Getting ads from remote data source');
      final ads = await remoteDataSource.getAds();
      return Right(ads);
    } on ServerException catch (e) {
      LoggerService.error('Server exception when getting ads', error: e);
      return Left(ServerFailure(message: e.message));
    } on UnauthorizedException catch (e) {
      LoggerService.error('Unauthorized exception when getting ads', error: e);
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      LoggerService.error('Unknown exception when getting ads', error: e);
      return Left(ServerFailure(message: e.toString()));
    }
  }
}
