import 'package:dartz/dartz.dart';

import '../../core/errors/exceptions.dart';
import '../../core/errors/failures.dart';
import '../../core/network/api_service.dart';
import '../../core/utils/logger.dart';
import '../../domain/entities/bus.dart';
import '../../domain/repositories/bus_repository.dart';
import '../models/bus_model.dart';

/// Bus repository implementation
/// Following Single Responsibility Principle by focusing only on bus data operations
class BusRepositoryImpl implements BusRepository {
  final ApiService _apiService;

  BusRepositoryImpl(this._apiService);

  @override
  Future<Either<Failure, List<Bus>>> getBuses({
    int page = 1,
    int perPage = 10,
    String? search,
  }) async {
    try {
      // Build URL with query parameters (matching existing buses_repo.dart pattern)
      String url = 'buses/all?page=$page&limit=$perPage';

      if (search != null && search.isNotEmpty) {
        url += '&text=$search';
      }

      LoggerService.debug('Sending API request for buses', data: {
        'url': url,
        'page': page,
        'limit': perPage,
        'search': search,
      });

      final response = await _apiService.get(
        url,
        isAuth: true,
      );

      final responseData = response.data;
      LoggerService.debug('Received buses response', data: responseData);

      // Check if response has errors (SchoolX API format - uses 'errors' field)
      if (responseData['errors'] == true) {
        final errorMessage = responseData['message'] ?? 'Failed to get buses';
        LoggerService.error('API returned error', data: {'message': errorMessage});
        return Left(ServerFailure(message: errorMessage));
      }

      // Parse buses from response data (SchoolX format)
      final List<dynamic> busesJson = responseData['data']['data'] ?? [];
      final List<Bus> buses = busesJson
          .map((busJson) => BusModel.fromJson(busJson))
          .toList();

      LoggerService.info('Successfully loaded ${buses.length} buses');
      return Right(buses);
    } on ServerException catch (e) {
      LoggerService.error('Server exception while getting buses', error: e);
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      LoggerService.error('Unexpected error while getting buses', error: e);
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, Bus>> getBusById(int id) async {
    try {
      LoggerService.debug('Getting bus by ID', data: {'id': id});

      final response = await _apiService.get(
        'buses/$id',
        isAuth: true,
      );

      final responseData = response.data;
      LoggerService.debug('Received bus details response', data: responseData);

      if (responseData['errors'] == true) {
        final errorMessage = responseData['message'] ?? 'Failed to get bus details';
        return Left(ServerFailure(message: errorMessage));
      }

      final busJson = responseData['data'];
      final bus = BusModel.fromJson(busJson);

      LoggerService.info('Successfully loaded bus details', data: {'busId': id});
      return Right(bus);
    } on ServerException catch (e) {
      LoggerService.error('Server exception while getting bus details', error: e);
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      LoggerService.error('Unexpected error while getting bus details', error: e);
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, Bus>> createBus(Bus bus) async {
    try {
      final busModel = bus as BusModel;

      LoggerService.debug('Creating bus', data: {
        'name': busModel.name,
        'carNumber': busModel.carNumber,
        'notes': busModel.notes,
      });

      final response = await _apiService.post(
        url: 'buses/store',
        body: busModel.toJson(),
        isAuth: true,
      );

      final responseData = response.data;
      LoggerService.debug('Received create bus response', data: responseData);

      // Check if response has errors (SchoolX API format - uses 'errors' field)
      if (responseData['errors'] == true) {
        final errorMessage = responseData['message'] ?? 'Failed to create bus';
        return Left(ServerFailure(message: errorMessage));
      }

      final createdBusJson = responseData['data'];
      final createdBus = BusModel.fromJson(createdBusJson);

      LoggerService.info('Successfully created bus', data: {'busId': createdBus.id});
      return Right(createdBus);
    } on ServerException catch (e) {
      LoggerService.error('Server exception while creating bus', error: e);
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      LoggerService.error('Unexpected error while creating bus', error: e);
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, Bus>> updateBus(Bus bus) async {
    try {
      final busModel = bus as BusModel;

      if (busModel.id == null) {
        return Left(ServerFailure(message: 'Bus ID is required for update'));
      }

      LoggerService.debug('Updating bus', data: {
        'busId': busModel.id,
        'name': busModel.name,
        'carNumber': busModel.carNumber,
        'notes': busModel.notes,
      });

      final response = await _apiService.put(
        url: 'buses/${busModel.id}',
        body: busModel.toJson(),
        isAuth: true,
      );

      final responseData = response.data;
      LoggerService.debug('Received update bus response', data: responseData);

      if (responseData['errors'] == true) {
        final errorMessage = responseData['message'] ?? 'Failed to update bus';
        return Left(ServerFailure(message: errorMessage));
      }

      final updatedBusJson = responseData['data'];
      final updatedBus = BusModel.fromJson(updatedBusJson);

      LoggerService.info('Successfully updated bus', data: {'busId': updatedBus.id});
      return Right(updatedBus);
    } on ServerException catch (e) {
      LoggerService.error('Server exception while updating bus', error: e);
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      LoggerService.error('Unexpected error while updating bus', error: e);
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, bool>> deleteBus(int id) async {
    try {
      LoggerService.debug('Deleting bus', data: {'busId': id});

      // Use the exact same endpoint format as original SchoolX app
      final response = await _apiService.delete(
        url: 'buses/destroy/$id',
        body: {}, // Empty body as in original implementation
        isAuth: true,
      );

      final responseData = response.data;
      LoggerService.debug('Received delete bus response', data: responseData);

      // Check response format exactly like original SchoolX app
      if (responseData['errors'] == true) {
        final errorMessage = responseData['message'] ?? 'Failed to delete bus';
        LoggerService.error('API returned error for bus deletion', data: {
          'busId': id,
          'error': errorMessage,
        });
        return Left(ServerFailure(message: errorMessage));
      }

      LoggerService.info('Successfully deleted bus', data: {
        'busId': id,
        'message': responseData['message'],
      });
      return const Right(true);
    } on ServerException catch (e) {
      LoggerService.error('Server exception while deleting bus', error: e);
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      LoggerService.error('Unexpected error while deleting bus', error: e);
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, List<Bus>>> getBusesByDriverId(String driverId) async {
    try {
      LoggerService.debug('Getting buses by driver ID', data: {'driverId': driverId});

      final response = await _apiService.get(
        'buses/driver/$driverId',
        isAuth: true,
      );

      final responseData = response.data;
      LoggerService.debug('Received buses by driver response', data: responseData);

      if (responseData['errors'] == true) {
        final errorMessage = responseData['message'] ?? 'Failed to get buses by driver';
        return Left(ServerFailure(message: errorMessage));
      }

      final List<dynamic> busesJson = responseData['data'] ?? [];
      final List<Bus> buses = busesJson
          .map((bus) => BusModel.fromJson(bus))
          .toList();

      LoggerService.info('Successfully loaded buses by driver', data: {
        'driverId': driverId,
        'busCount': buses.length,
      });
      return Right(buses);
    } on ServerException catch (e) {
      LoggerService.error('Server exception while getting buses by driver', error: e);
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      LoggerService.error('Unexpected error while getting buses by driver', error: e);
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, List<Bus>>> getAvailableBuses() async {
    try {
      LoggerService.debug('Getting available buses');

      final response = await _apiService.get(
        'buses/show/availableAdd/driver',
        isAuth: true,
      );

      final responseData = response.data;
      LoggerService.debug('Received available buses response', data: responseData);

      if (responseData['errors'] == true) {
        final errorMessage = responseData['message'] ?? 'Failed to get available buses';
        return Left(ServerFailure(message: errorMessage));
      }

      final List<dynamic> busesJson = responseData['data'] ?? [];
      final List<Bus> buses = busesJson
          .map((bus) => BusModel.fromJson(bus))
          .toList();

      LoggerService.info('Successfully loaded available buses', data: {
        'busCount': buses.length,
      });
      return Right(buses);
    } on ServerException catch (e) {
      LoggerService.error('Server exception while getting available buses', error: e);
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      LoggerService.error('Unexpected error while getting available buses', error: e);
      return Left(ServerFailure(message: e.toString()));
    }
  }
}
