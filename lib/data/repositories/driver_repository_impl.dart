import 'package:dartz/dartz.dart';
import '../../core/errors/failures.dart';
import '../../core/errors/exceptions.dart';
import '../../core/utils/logger.dart';
import '../../core/network/api_service.dart';
import '../../domain/entities/driver.dart';
import '../../domain/repositories/driver_repository.dart';
import '../models/driver_model.dart';

/// Driver repository implementation
/// Following Single Responsibility Principle by focusing only on driver data operations
class DriverRepositoryImpl implements DriverRepository {
  final ApiService _apiService;

  DriverRepositoryImpl({required ApiService apiService}) : _apiService = apiService;

  @override
  Future<Either<Failure, List<Driver>>> getDrivers({
    int page = 1,
    int limit = 10,
    String? search,
  }) async {
    try {
      LoggerService.debug('Fetching drivers', data: {
        'page': page,
        'limit': limit,
        'search': search,
      });

      // Build URL with search parameter if provided
      String url = 'attendants/all/driver?page=$page&limit=$limit';
      if (search != null && search.isNotEmpty) {
        url += '&text=$search';
      }

      final response = await _apiService.get(
        url,
        isAuth: true,
      );

      final responseData = response.data;
      LoggerService.debug('Received drivers response', data: responseData);

      // Check for API errors (matching original SchoolX format)
      if (responseData['errors'] == true) {
        final errorMessage = responseData['message'] ?? 'Failed to fetch drivers';
        return Left(ServerFailure(message: errorMessage));
      }

      // Parse drivers data (matching original SchoolX response structure)
      final driversData = responseData['data']?['data'] as List<dynamic>?;
      if (driversData == null) {
        LoggerService.warning('No drivers data found in response');
        return const Right([]);
      }

      final drivers = driversData
          .map((json) => DriverModel.fromJson(json as Map<String, dynamic>))
          .toList();

      LoggerService.info('Successfully fetched ${drivers.length} drivers');
      return Right(drivers);
    } on ServerException catch (e) {
      LoggerService.error('Server exception while fetching drivers', error: e);
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      LoggerService.error('Unexpected error while fetching drivers', error: e);
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, Driver>> getDriverById(int id) async {
    try {
      LoggerService.debug('Fetching driver by ID', data: {'driverId': id});

      final response = await _apiService.get(
        'attendants/show/driver/$id',
        isAuth: true,
      );

      final responseData = response.data;
      LoggerService.debug('Received driver response', data: responseData);

      if (responseData['errors'] == true) {
        final errorMessage = responseData['message'] ?? 'Driver not found';
        return Left(ServerFailure(message: errorMessage));
      }

      final driverData = responseData['data'];
      if (driverData == null) {
        return Left(ServerFailure(message: 'Driver not found'));
      }

      final driver = DriverModel.fromJson(driverData as Map<String, dynamic>);
      LoggerService.info('Successfully fetched driver', data: {'driverId': id});
      return Right(driver);
    } on ServerException catch (e) {
      LoggerService.error('Server exception while fetching driver', error: e);
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      LoggerService.error('Unexpected error while fetching driver', error: e);
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, Driver>> createDriver(Driver driver, {
    String? password,
    String? passwordConfirmation,
  }) async {
    try {
      LoggerService.debug('Creating driver', data: {'driverName': driver.name});

      // Prepare form data (matching original SchoolX format)
      final driverModel = driver as DriverModel;
      final formData = driverModel.toCreateJson();
      
      // Add password fields if provided
      if (password != null && password.isNotEmpty) {
        formData['password'] = password;
        formData['password_confirmation'] = passwordConfirmation ?? password;
      }

      final response = await _apiService.post(
        url: 'attendants/store/driver',
        body: formData,
        isAuth: true,
      );

      final responseData = response.data;
      LoggerService.debug('Received create driver response', data: responseData);

      if (responseData['errors'] == true) {
        final errorMessage = responseData['message'] ?? 'Failed to create driver';
        return Left(ServerFailure(message: errorMessage));
      }

      final createdDriverData = responseData['data'];
      if (createdDriverData == null) {
        return Left(ServerFailure(message: 'Failed to create driver'));
      }

      final createdDriver = DriverModel.fromJson(createdDriverData as Map<String, dynamic>);
      LoggerService.info('Successfully created driver', data: {'driverId': createdDriver.id});
      return Right(createdDriver);
    } on ServerException catch (e) {
      LoggerService.error('Server exception while creating driver', error: e);
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      LoggerService.error('Unexpected error while creating driver', error: e);
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, Driver>> updateDriver(Driver driver, {
    String? password,
  }) async {
    try {
      LoggerService.debug('Updating driver', data: {'driverId': driver.id});

      // Prepare form data (matching original SchoolX format)
      final driverModel = driver as DriverModel;
      final formData = driverModel.toUpdateJson();
      
      // Add password field if provided
      if (password != null && password.isNotEmpty) {
        formData['password'] = password;
      }

      final response = await _apiService.post(
        url: 'attendants/update/driver/${driver.id}',
        body: formData,
        isAuth: true,
      );

      final responseData = response.data;
      LoggerService.debug('Received update driver response', data: responseData);

      if (responseData['errors'] == true) {
        final errorMessage = responseData['message'] ?? 'Failed to update driver';
        return Left(ServerFailure(message: errorMessage));
      }

      final updatedDriverData = responseData['data'];
      if (updatedDriverData == null) {
        return Left(ServerFailure(message: 'Failed to update driver'));
      }

      final updatedDriver = DriverModel.fromJson(updatedDriverData as Map<String, dynamic>);
      LoggerService.info('Successfully updated driver', data: {'driverId': driver.id});
      return Right(updatedDriver);
    } on ServerException catch (e) {
      LoggerService.error('Server exception while updating driver', error: e);
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      LoggerService.error('Unexpected error while updating driver', error: e);
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, bool>> deleteDriver(int id) async {
    try {
      LoggerService.debug('Deleting driver', data: {'driverId': id});

      // Use the exact same endpoint format as original SchoolX app
      final response = await _apiService.delete(
        url: 'attendants/destroy/$id',
        body: {},
        isAuth: true,
      );

      final responseData = response.data;
      LoggerService.debug('Received delete driver response', data: responseData);

      // Check response format exactly like original SchoolX app
      if (responseData['errors'] == true) {
        final errorMessage = responseData['message'] ?? 'Failed to delete driver';
        LoggerService.error('API returned error for driver deletion', data: {
          'driverId': id,
          'error': errorMessage,
        });
        return Left(ServerFailure(message: errorMessage));
      }

      LoggerService.info('Successfully deleted driver', data: {
        'driverId': id,
        'message': responseData['message'],
      });
      return const Right(true);
    } on ServerException catch (e) {
      LoggerService.error('Server exception while deleting driver', error: e);
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      LoggerService.error('Unexpected error while deleting driver', error: e);
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, List<Map<String, dynamic>>>> getAvailableBuses() async {
    try {
      LoggerService.debug('Fetching available buses for driver assignment');

      final response = await _apiService.get(
        'buses/show/availableAdd/driver',
        isAuth: true,
      );

      final responseData = response.data;
      LoggerService.debug('Received available buses response', data: responseData);

      if (responseData['errors'] == true) {
        final errorMessage = responseData['message'] ?? 'Failed to fetch available buses';
        return Left(ServerFailure(message: errorMessage));
      }

      final busesData = responseData['data'] as List<dynamic>?;
      if (busesData == null) {
        return const Right([]);
      }

      final buses = busesData.cast<Map<String, dynamic>>();
      LoggerService.info('Successfully fetched ${buses.length} available buses');
      return Right(buses);
    } on ServerException catch (e) {
      LoggerService.error('Server exception while fetching available buses', error: e);
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      LoggerService.error('Unexpected error while fetching available buses', error: e);
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, List<Map<String, dynamic>>>> getGenderOptions() async {
    try {
      LoggerService.debug('Fetching gender options');

      final response = await _apiService.get(
        'general/gender',
        isAuth: true,
      );

      final responseData = response.data;
      if (responseData['errors'] == true) {
        final errorMessage = responseData['message'] ?? 'Failed to fetch gender options';
        return Left(ServerFailure(message: errorMessage));
      }

      final genderData = responseData['data'] as List<dynamic>?;
      if (genderData == null) {
        return const Right([]);
      }

      final genders = genderData.cast<Map<String, dynamic>>();
      return Right(genders);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, List<Map<String, dynamic>>>> getReligionOptions() async {
    try {
      LoggerService.debug('Fetching religion options');

      final response = await _apiService.get(
        'general/religion',
        isAuth: true,
      );

      final responseData = response.data;
      if (responseData['errors'] == true) {
        final errorMessage = responseData['message'] ?? 'Failed to fetch religion options';
        return Left(ServerFailure(message: errorMessage));
      }

      final religionData = responseData['data'] as List<dynamic>?;
      if (religionData == null) {
        return const Right([]);
      }

      final religions = religionData.cast<Map<String, dynamic>>();
      return Right(religions);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, List<Map<String, dynamic>>>> getBloodTypeOptions() async {
    try {
      LoggerService.debug('Fetching blood type options');

      final response = await _apiService.get(
        'general/type/blood',
        isAuth: true,
      );

      final responseData = response.data;
      if (responseData['errors'] == true) {
        final errorMessage = responseData['message'] ?? 'Failed to fetch blood type options';
        return Left(ServerFailure(message: errorMessage));
      }

      final bloodTypeData = responseData['data'] as List<dynamic>?;
      if (bloodTypeData == null) {
        return const Right([]);
      }

      final bloodTypes = bloodTypeData.cast<Map<String, dynamic>>();
      return Right(bloodTypes);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }
}
