import 'package:dartz/dartz.dart';

import '../../core/errors/exceptions.dart';
import '../../core/errors/failures.dart';
import '../../core/utils/logger.dart';
import '../../domain/entities/trip.dart';
import '../../domain/repositories/trip_repository.dart';
import '../datasources/trip_remote_data_source.dart';

/// TripRepositoryImpl class for implementing TripRepository
/// Following Repository Pattern and Dependency Inversion Principle
class TripRepositoryImpl implements TripRepository {
  final TripRemoteDataSource remoteDataSource;

  TripRepositoryImpl({required this.remoteDataSource});

  @override
  Future<Either<Failure, Trip?>> getCurrentTrip() async {
    try {
      LoggerService.info('Getting current trip from remote data source');
      final trip = await remoteDataSource.getCurrentTrip();
      return Right(trip);
    } on ServerException catch (e) {
      LoggerService.error(
        'Server exception when getting current trip',
        error: e,
      );
      return Left(ServerFailure(message: e.message));
    } on UnauthorizedException catch (e) {
      LoggerService.error(
        'Unauthorized exception when getting current trip',
        error: e,
      );
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      LoggerService.error(
        'Unknown exception when getting current trip',
        error: e,
      );
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, Trip>> getTripById(String id) async {
    try {
      LoggerService.info(
        'Getting trip by id from remote data source',
        data: {'id': id},
      );
      final trip = await remoteDataSource.getTripById(id);
      return Right(trip);
    } on ServerException catch (e) {
      LoggerService.error('Server exception when getting trip by id', error: e);
      return Left(ServerFailure(message: e.message));
    } on UnauthorizedException catch (e) {
      LoggerService.error(
        'Unauthorized exception when getting trip by id',
        error: e,
      );
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      LoggerService.error(
        'Unknown exception when getting trip by id',
        error: e,
      );
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, List<Trip>>> getUpcomingTrips() async {
    try {
      LoggerService.info('Getting upcoming trips from remote data source');
      final trips = await remoteDataSource.getUpcomingTrips();
      return Right(trips);
    } on ServerException catch (e) {
      LoggerService.error(
        'Server exception when getting upcoming trips',
        error: e,
      );
      return Left(ServerFailure(message: e.message));
    } on UnauthorizedException catch (e) {
      LoggerService.error(
        'Unauthorized exception when getting upcoming trips',
        error: e,
      );
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      LoggerService.error(
        'Unknown exception when getting upcoming trips',
        error: e,
      );
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, List<Trip>>> getRecentTrips() async {
    try {
      LoggerService.info('Getting recent trips from remote data source');
      final trips = await remoteDataSource.getRecentTrips();
      return Right(trips);
    } on ServerException catch (e) {
      LoggerService.error(
        'Server exception when getting recent trips',
        error: e,
      );
      return Left(ServerFailure(message: e.message));
    } on UnauthorizedException catch (e) {
      LoggerService.error(
        'Unauthorized exception when getting recent trips',
        error: e,
      );
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      LoggerService.error(
        'Unknown exception when getting recent trips',
        error: e,
      );
      return Left(ServerFailure(message: e.toString()));
    }
  }
}
