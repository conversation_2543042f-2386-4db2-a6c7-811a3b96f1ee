import 'package:equatable/equatable.dart';

/// Announcement entity
/// Following Single Responsibility Principle by focusing only on announcement data
class Announcement extends Equatable {
  final String id;
  final String title;
  final String content;
  final DateTime date;
  final String authorName;
  final String? authorId;
  final bool isImportant;
  
  const Announcement({
    required this.id,
    required this.title,
    required this.content,
    required this.date,
    required this.authorName,
    this.authorId,
    this.isImportant = false,
  });
  
  @override
  List<Object?> get props => [
    id,
    title,
    content,
    date,
    authorName,
    authorId,
    isImportant,
  ];
}
