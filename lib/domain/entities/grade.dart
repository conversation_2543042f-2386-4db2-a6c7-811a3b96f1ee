import 'package:equatable/equatable.dart';

/// Grade entity
class Grade extends Equatable {
  final int? id;
  final String? name;
  final String? createdAt;
  final String? updatedAt;

  const Grade({
    this.id,
    this.name,
    this.createdAt,
    this.updatedAt,
  });

  /// Create a copy of this Grade with the given fields replaced with the new values
  Grade copyWith({
    int? id,
    String? name,
    String? createdAt,
    String? updatedAt,
  }) {
    return Grade(
      id: id ?? this.id,
      name: name ?? this.name,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  List<Object?> get props => [
        id,
        name,
        createdAt,
        updatedAt,
      ];
}
