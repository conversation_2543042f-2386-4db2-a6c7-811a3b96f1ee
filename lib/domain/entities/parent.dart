import 'package:equatable/equatable.dart';

/// Parent entity
class Parent extends Equatable {
  final int? id;
  final String? name;
  final String? email;
  final String? phone;
  final String? address;
  final String? createdAt;
  final String? updatedAt;

  const Parent({
    this.id,
    this.name,
    this.email,
    this.phone,
    this.address,
    this.createdAt,
    this.updatedAt,
  });

  /// Create a copy of this Parent with the given fields replaced with the new values
  Parent copyWith({
    int? id,
    String? name,
    String? email,
    String? phone,
    String? address,
    String? createdAt,
    String? updatedAt,
  }) {
    return Parent(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      address: address ?? this.address,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  List<Object?> get props => [
        id,
        name,
        email,
        phone,
        address,
        createdAt,
        updatedAt,
      ];
}
