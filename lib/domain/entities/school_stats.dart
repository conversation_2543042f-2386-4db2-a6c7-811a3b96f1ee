import 'package:equatable/equatable.dart';

/// SchoolStats entity
/// Following Single Responsibility Principle by focusing only on school stats data
class SchoolStats extends Equatable {
  final String id;
  final String name;
  final int studentCount;
  final int busCount;
  final int tripCount;
  final int supervisorCount;
  final int driverCount;
  final DateTime lastTripDate;

  const SchoolStats({
    required this.id,
    required this.name,
    required this.studentCount,
    required this.busCount,
    required this.tripCount,
    required this.supervisorCount,
    required this.driverCount,
    required this.lastTripDate,
  });

  @override
  List<Object?> get props => [
        id,
        name,
        studentCount,
        busCount,
        tripCount,
        supervisorCount,
        driverCount,
        lastTripDate,
      ];
}
