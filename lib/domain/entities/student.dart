import 'package:equatable/equatable.dart';
import 'bus.dart';
import 'grade.dart';
import 'parent.dart';
import 'type_blood.dart';

/// Student entity
class Student extends Equatable {
  final String? id;
  final String? name;
  final String? phone;
  final String? cityName;
  final String? address;
  final String? logoPath;
  final String? updatedAt;
  final String? createdAt;
  final String? deletedAt;
  final String? logo;
  final String? longitude;
  final String? latitude;
  final String? attendantAdminsId;
  final String? attendantDriverId;
  final int? busId;
  final int? classroomId;
  final String? dateBirth;
  final int? genderId;
  final int? gradeId;
  final String? parentKey;
  final String? parentSecret;
  final int? religionId;
  final int? schoolId;
  final String? tripType;
  final int? typeBloodId;
  final String? schools;
  final String? gender;
  final String? religion;
  final TypeBlood? typeBlood;
  final Bus? bus;
  final String? classroom;
  final String? attendantAdmins;
  final String? attendantDriver;
  final String? attendance;
  final Grade? grade;
  final List<Parent>? parents;

  const Student({
    this.id,
    this.name,
    this.phone,
    this.cityName,
    this.address,
    this.logoPath,
    this.updatedAt,
    this.createdAt,
    this.deletedAt,
    this.logo,
    this.longitude,
    this.latitude,
    this.attendantAdminsId,
    this.attendantDriverId,
    this.busId,
    this.classroomId,
    this.dateBirth,
    this.genderId,
    this.gradeId,
    this.parentKey,
    this.parentSecret,
    this.religionId,
    this.schoolId,
    this.tripType,
    this.typeBloodId,
    this.schools,
    this.gender,
    this.religion,
    this.typeBlood,
    this.bus,
    this.classroom,
    this.attendantAdmins,
    this.attendantDriver,
    this.attendance,
    this.grade,
    this.parents,
  });

  /// Create a copy of this Student with the given fields replaced with the new values
  Student copyWith({
    String? id,
    String? name,
    String? phone,
    String? cityName,
    String? address,
    String? logoPath,
    String? updatedAt,
    String? createdAt,
    String? deletedAt,
    String? logo,
    String? longitude,
    String? latitude,
    String? attendantAdminsId,
    String? attendantDriverId,
    int? busId,
    int? classroomId,
    String? dateBirth,
    int? genderId,
    int? gradeId,
    String? parentKey,
    String? parentSecret,
    int? religionId,
    int? schoolId,
    String? tripType,
    int? typeBloodId,
    String? schools,
    String? gender,
    String? religion,
    TypeBlood? typeBlood,
    Bus? bus,
    String? classroom,
    String? attendantAdmins,
    String? attendantDriver,
    String? attendance,
    Grade? grade,
    List<Parent>? parents,
  }) {
    return Student(
      id: id ?? this.id,
      name: name ?? this.name,
      phone: phone ?? this.phone,
      cityName: cityName ?? this.cityName,
      address: address ?? this.address,
      logoPath: logoPath ?? this.logoPath,
      updatedAt: updatedAt ?? this.updatedAt,
      createdAt: createdAt ?? this.createdAt,
      deletedAt: deletedAt ?? this.deletedAt,
      logo: logo ?? this.logo,
      longitude: longitude ?? this.longitude,
      latitude: latitude ?? this.latitude,
      attendantAdminsId: attendantAdminsId ?? this.attendantAdminsId,
      attendantDriverId: attendantDriverId ?? this.attendantDriverId,
      busId: busId ?? this.busId,
      classroomId: classroomId ?? this.classroomId,
      dateBirth: dateBirth ?? this.dateBirth,
      genderId: genderId ?? this.genderId,
      gradeId: gradeId ?? this.gradeId,
      parentKey: parentKey ?? this.parentKey,
      parentSecret: parentSecret ?? this.parentSecret,
      religionId: religionId ?? this.religionId,
      schoolId: schoolId ?? this.schoolId,
      tripType: tripType ?? this.tripType,
      typeBloodId: typeBloodId ?? this.typeBloodId,
      schools: schools ?? this.schools,
      gender: gender ?? this.gender,
      religion: religion ?? this.religion,
      typeBlood: typeBlood ?? this.typeBlood,
      bus: bus ?? this.bus,
      classroom: classroom ?? this.classroom,
      attendantAdmins: attendantAdmins ?? this.attendantAdmins,
      attendantDriver: attendantDriver ?? this.attendantDriver,
      attendance: attendance ?? this.attendance,
      grade: grade ?? this.grade,
      parents: parents ?? this.parents,
    );
  }

  @override
  List<Object?> get props => [
        id,
        name,
        phone,
        cityName,
        address,
        logoPath,
        updatedAt,
        createdAt,
        deletedAt,
        logo,
        longitude,
        latitude,
        attendantAdminsId,
        attendantDriverId,
        busId,
        classroomId,
        dateBirth,
        genderId,
        gradeId,
        parentKey,
        parentSecret,
        religionId,
        schoolId,
        tripType,
        typeBloodId,
        schools,
        gender,
        religion,
        typeBlood,
        bus,
        classroom,
        attendantAdmins,
        attendantDriver,
        attendance,
        grade,
        parents,
      ];
}
