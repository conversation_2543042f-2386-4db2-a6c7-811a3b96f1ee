import 'package:equatable/equatable.dart';

/// Trip entity
/// Following Single Responsibility Principle by focusing only on trip data
class Trip extends Equatable {
  final String id;
  final String name;
  final String schoolId;
  final String schoolName;
  final String busId;
  final String busNumber;
  final String driverId;
  final String driverName;
  final String supervisorId;
  final String supervisorName;
  final DateTime startTime;
  final DateTime? endTime;
  final String status;
  final List<String> studentIds;
  final List<String> attendedStudentIds;
  final String startLocation;
  final String endLocation;
  final double distance;
  final int duration;
  final List<TripStop> stops;
  final List<TripEvent> events;

  const Trip({
    required this.id,
    required this.name,
    required this.schoolId,
    required this.schoolName,
    required this.busId,
    required this.busNumber,
    required this.driverId,
    required this.driverName,
    required this.supervisorId,
    required this.supervisorName,
    required this.startTime,
    this.endTime,
    required this.status,
    required this.studentIds,
    required this.attendedStudentIds,
    required this.startLocation,
    required this.endLocation,
    required this.distance,
    required this.duration,
    required this.stops,
    required this.events,
  });

  @override
  List<Object?> get props => [
        id,
        name,
        schoolId,
        schoolName,
        busId,
        busN<PERSON>ber,
        driverId,
        driverName,
        supervisorId,
        supervisorName,
        startTime,
        endTime,
        status,
        studentIds,
        attendedStudentIds,
        startLocation,
        endLocation,
        distance,
        duration,
        stops,
        events,
      ];
      
  /// Get trip progress percentage
  double get progressPercentage {
    if (status == 'completed') {
      return 100.0;
    } else if (status == 'cancelled') {
      return 0.0;
    } else if (status == 'in_progress') {
      // Calculate progress based on stops
      if (stops.isEmpty) {
        return 0.0;
      }
      
      final completedStops = stops.where((stop) => stop.status == 'completed').length;
      return (completedStops / stops.length) * 100;
    } else {
      return 0.0;
    }
  }
  
  /// Get trip attendance percentage
  double get attendancePercentage {
    if (studentIds.isEmpty) {
      return 0.0;
    }
    
    return (attendedStudentIds.length / studentIds.length) * 100;
  }
}

/// TripStop entity
class TripStop extends Equatable {
  final String id;
  final String name;
  final String location;
  final DateTime scheduledTime;
  final DateTime? actualTime;
  final String status;
  final List<String> studentIds;
  final List<String> attendedStudentIds;

  const TripStop({
    required this.id,
    required this.name,
    required this.location,
    required this.scheduledTime,
    this.actualTime,
    required this.status,
    required this.studentIds,
    required this.attendedStudentIds,
  });

  @override
  List<Object?> get props => [
        id,
        name,
        location,
        scheduledTime,
        actualTime,
        status,
        studentIds,
        attendedStudentIds,
      ];
}

/// TripEvent entity
class TripEvent extends Equatable {
  final String id;
  final String type;
  final String description;
  final DateTime time;
  final String location;
  final String userId;
  final String userName;

  const TripEvent({
    required this.id,
    required this.type,
    required this.description,
    required this.time,
    required this.location,
    required this.userId,
    required this.userName,
  });

  @override
  List<Object?> get props => [
        id,
        type,
        description,
        time,
        location,
        userId,
        userName,
      ];
}
