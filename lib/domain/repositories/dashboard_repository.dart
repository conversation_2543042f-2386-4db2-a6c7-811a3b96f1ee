import 'package:dartz/dartz.dart';
import '../../core/errors/failures.dart';
import '../entities/dashboard_stats.dart';
import '../entities/school_stats.dart';
import '../entities/trip.dart';

/// DashboardRepository interface
/// Following Interface Segregation Principle by creating a specific interface for dashboard operations
abstract class DashboardRepository {
  /// Get dashboard statistics
  Future<Either<Failure, DashboardStats>> getDashboardStats();
  
  /// Get school statistics
  Future<Either<Failure, List<SchoolStats>>> getSchoolStats();
  
  /// Get recent trips
  Future<Either<Failure, List<Trip>>> getRecentTrips();
  
  /// Get upcoming trips
  Future<Either<Failure, List<Trip>>> getUpcomingTrips();
}
