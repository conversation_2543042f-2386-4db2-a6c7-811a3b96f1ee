import 'package:dartz/dartz.dart';
import '../../core/error/failures.dart';
import '../entities/driver.dart';

/// Driver repository interface
/// Following Single Responsibility Principle by focusing only on driver data operations
abstract class DriverRepository {
  /// Get all drivers with pagination and search
  Future<Either<Failure, List<Driver>>> getDrivers({
    int page = 1,
    int limit = 10,
    String? search,
  });

  /// Get driver by ID
  Future<Either<Failure, Driver>> getDriverById(int id);

  /// Create new driver
  Future<Either<Failure, Driver>> createDriver(Driver driver, {
    String? password,
    String? passwordConfirmation,
  });

  /// Update existing driver
  Future<Either<Failure, Driver>> updateDriver(Driver driver, {
    String? password,
  });

  /// Delete driver
  Future<Either<Failure, bool>> deleteDriver(int id);

  /// Get available buses for driver assignment
  Future<Either<Failure, List<Map<String, dynamic>>>> getAvailableBuses();

  /// Get gender options
  Future<Either<Failure, List<Map<String, dynamic>>>> getGenderOptions();

  /// Get religion options
  Future<Either<Failure, List<Map<String, dynamic>>>> getReligionOptions();

  // فصيلة الدم غير مستخدمة في التطبيق الأصلي
  // Future<Either<Failure, List<Map<String, dynamic>>>> getBloodTypeOptions();
}
