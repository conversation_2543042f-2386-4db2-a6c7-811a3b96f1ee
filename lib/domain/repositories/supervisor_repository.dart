import 'package:dartz/dartz.dart';
import '../entities/supervisor.dart';
import '../../core/error/failures.dart';

/// Supervisor repository interface
/// Following Single Responsibility Principle by focusing only on supervisor data operations
abstract class SupervisorRepository {
  /// Get all supervisors with pagination
  Future<Either<Failure, List<Supervisor>>> getSupervisors({
    int page = 1,
    int limit = 10,
  });

  /// Get supervisor by ID
  Future<Either<Failure, Supervisor>> getSupervisorById(int id);

  /// Create new supervisor
  Future<Either<Failure, Supervisor>> createSupervisor(Supervisor supervisor);

  /// Update existing supervisor
  Future<Either<Failure, Supervisor>> updateSupervisor(Supervisor supervisor);

  /// Delete supervisor
  Future<Either<Failure, bool>> deleteSupervisor(int id);

  /// Get available buses for supervisor assignment
  Future<Either<Failure, List<Map<String, dynamic>>>> getAvailableBuses();

  /// Get gender options for supervisor form
  Future<Either<Failure, List<Map<String, dynamic>>>> getGenderOptions();

  /// Get religion options for supervisor form
  Future<Either<Failure, List<Map<String, dynamic>>>> getReligionOptions();

  /// Get blood type options for supervisor form
  Future<Either<Failure, List<Map<String, dynamic>>>> getBloodTypeOptions();
}
