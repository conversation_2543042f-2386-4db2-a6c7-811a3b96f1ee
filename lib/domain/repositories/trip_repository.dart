import 'package:dartz/dartz.dart';

import '../../core/errors/failures.dart';
import '../entities/trip.dart';

/// TripRepository interface
/// Following Dependency Inversion Principle
abstract class TripRepository {
  /// Get current trip
  Future<Either<Failure, Trip?>> getCurrentTrip();
  
  /// Get trip by id
  Future<Either<Failure, Trip>> getTripById(String id);
  
  /// Get upcoming trips
  Future<Either<Failure, List<Trip>>> getUpcomingTrips();
  
  /// Get recent trips
  Future<Either<Failure, List<Trip>>> getRecentTrips();
}
