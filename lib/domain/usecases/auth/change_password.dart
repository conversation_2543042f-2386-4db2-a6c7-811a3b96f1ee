import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../../core/errors/failures.dart';
import '../../../core/usecases/usecase.dart';
import '../../repositories/user_repository.dart';

/// ChangePassword use case
/// Following Single Responsibility Principle by focusing only on changing password
class ChangePassword implements UseCase<bool, ChangePasswordParams> {
  final UserRepository repository;

  ChangePassword(this.repository);

  @override
  Future<Either<Failure, bool>> call(ChangePasswordParams params) async {
    return await repository.changePassword(
      params.currentPassword,
      params.newPassword,
    );
  }
}

/// ChangePasswordParams class for change password use case parameters
class ChangePasswordParams extends Equatable {
  final String currentPassword;
  final String newPassword;

  const ChangePasswordParams({
    required this.currentPassword,
    required this.newPassword,
  });

  @override
  List<Object> get props => [currentPassword, newPassword];
}
