import 'package:dartz/dartz.dart';
import '../../../core/errors/failures.dart';
import '../../../core/usecases/usecase.dart';
import '../../repositories/user_repository.dart';

/// LogoutUser use case
/// Following Single Responsibility Principle by focusing only on user logout
class LogoutUser implements NoParamsUseCase<bool> {
  final UserRepository repository;

  LogoutUser(this.repository);

  @override
  Future<Either<Failure, bool>> call() async {
    return await repository.logout();
  }
}
