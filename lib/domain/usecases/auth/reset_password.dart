import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../../core/errors/failures.dart';
import '../../../core/usecases/usecase.dart';
import '../../repositories/user_repository.dart';

/// ResetPassword use case
/// Following Single Responsibility Principle by focusing only on reset password functionality
class ResetPassword implements UseCase<bool, ResetPasswordParams> {
  final UserRepository repository;

  ResetPassword(this.repository);

  @override
  Future<Either<Failure, bool>> call(ResetPasswordParams params) async {
    return await repository.resetPassword(
      params.email,
      params.code,
      params.password,
    );
  }
}

/// ResetPasswordParams class for reset password use case parameters
class ResetPasswordParams extends Equatable {
  final String email;
  final String code;
  final String password;

  const ResetPasswordParams({
    required this.email,
    required this.code,
    required this.password,
  });

  @override
  List<Object> get props => [email, code, password];
}
