import 'package:dartz/dartz.dart';
import '../entities/supervisor.dart';
import '../repositories/supervisor_repository.dart';
import '../../core/error/failures.dart';
import '../../core/usecases/usecase.dart';

/// Use case for creating supervisor
/// Following Single Responsibility Principle by focusing only on creating supervisor
class CreateSupervisorUseCase implements UseCase<Supervisor, Supervisor> {
  final SupervisorRepository repository;

  CreateSupervisorUseCase(this.repository);

  @override
  Future<Either<Failure, Supervisor>> call(Supervisor supervisor) async {
    return await repository.createSupervisor(supervisor);
  }
}
