import 'package:dartz/dartz.dart';
import '../../../core/errors/failures.dart';
import '../../../core/usecases/usecase.dart';
import '../../entities/dashboard_stats.dart';
import '../../repositories/dashboard_repository.dart';

/// GetDashboardStats use case
/// Following Single Responsibility Principle by focusing only on getting dashboard stats
class GetDashboardStats implements NoParamsUseCase<DashboardStats> {
  final DashboardRepository repository;

  GetDashboardStats(this.repository);

  @override
  Future<Either<Failure, DashboardStats>> call() async {
    return await repository.getDashboardStats();
  }
}
