import 'package:dartz/dartz.dart';
import '../../../core/errors/failures.dart';
import '../../../core/usecases/usecase.dart';
import '../../entities/trip.dart';
import '../../repositories/dashboard_repository.dart';

/// GetRecentTrips use case
/// Following Single Responsibility Principle by focusing only on getting recent trips
class GetRecentTrips implements NoParamsUseCase<List<Trip>> {
  final DashboardRepository repository;

  GetRecentTrips(this.repository);

  @override
  Future<Either<Failure, List<Trip>>> call() async {
    return await repository.getRecentTrips();
  }
}
