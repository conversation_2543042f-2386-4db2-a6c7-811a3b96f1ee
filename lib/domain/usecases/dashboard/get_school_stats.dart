import 'package:dartz/dartz.dart';
import '../../../core/errors/failures.dart';
import '../../../core/usecases/usecase.dart';
import '../../entities/school_stats.dart';
import '../../repositories/dashboard_repository.dart';

/// GetSchoolStats use case
/// Following Single Responsibility Principle by focusing only on getting school stats
class GetSchoolStats implements NoParamsUseCase<List<SchoolStats>> {
  final DashboardRepository repository;

  GetSchoolStats(this.repository);

  @override
  Future<Either<Failure, List<SchoolStats>>> call() async {
    return await repository.getSchoolStats();
  }
}
