import 'package:dartz/dartz.dart';

import '../../core/errors/failures.dart';
import '../entities/trip.dart';
import '../repositories/trip_repository.dart';

/// GetCurrentTrip use case
/// Following Single Responsibility Principle by focusing only on getting current trip
class GetCurrentTrip {
  final TripRepository repository;

  GetCurrentTrip(this.repository);

  /// Call method to execute the use case
  Future<Either<Failure, Trip?>> call() async {
    return await repository.getCurrentTrip();
  }
}
