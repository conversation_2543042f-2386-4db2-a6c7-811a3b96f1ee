import 'package:dartz/dartz.dart';

import '../../core/errors/failures.dart';
import '../entities/user.dart';
import '../repositories/user_repository.dart';

/// GetProfile use case
/// Following Single Responsibility Principle by focusing only on getting user profile
class GetProfile {
  final UserRepository repository;

  GetProfile(this.repository);

  /// Call method to execute the use case
  Future<Either<Failure, User>> call() async {
    return await repository.getCurrentUser();
  }
}
