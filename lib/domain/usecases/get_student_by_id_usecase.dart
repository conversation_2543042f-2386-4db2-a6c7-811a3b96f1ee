import 'package:busaty/core/errors/failures.dart';
import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';

import '../../core/usecases/usecase.dart';
import '../entities/student.dart';
import '../repositories/student_repository.dart';

/// Get student by id use case
class GetStudentByIdUseCase implements UseCase<Student, GetStudentByIdParams> {
  final StudentRepository repository;

  GetStudentByIdUseCase(this.repository);

  @override
  Future<Either<Failure, Student>> call(GetStudentByIdParams params) async {
    return await repository.getStudentById(params.id);
  }
}

/// Get student by id params
class GetStudentByIdParams extends Equatable {
  final String id;

  const GetStudentByIdParams({
    required this.id,
  });

  @override
  List<Object> get props => [id];
}
