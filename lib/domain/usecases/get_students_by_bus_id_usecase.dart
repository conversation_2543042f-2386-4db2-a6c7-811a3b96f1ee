import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';

import '../../core/errors/failures.dart';
import '../../core/usecases/usecase.dart';
import '../entities/student.dart';
import '../repositories/student_repository.dart';

/// Get students by bus ID use case
class GetStudentsByBusIdUseCase implements UseCase<List<Student>, GetStudentsByBusIdParams> {
  final StudentRepository repository;

  GetStudentsByBusIdUseCase(this.repository);

  @override
  Future<Either<Failure, List<Student>>> call(GetStudentsByBusIdParams params) async {
    return await repository.getStudentsByBusId(params.busId);
  }
}

/// Get students by bus ID params
class GetStudentsByBusIdParams extends Equatable {
  final String busId;

  const GetStudentsByBusIdParams({
    required this.busId,
  });

  @override
  List<Object> get props => [busId];
}
