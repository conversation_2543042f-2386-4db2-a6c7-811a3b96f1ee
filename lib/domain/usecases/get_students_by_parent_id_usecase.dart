import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';

import '../../core/errors/failures.dart';
import '../../core/usecases/usecase.dart';
import '../entities/student.dart';
import '../repositories/student_repository.dart';

/// Get students by parent ID use case
class GetStudentsByParentIdUseCase implements UseCase<List<Student>, GetStudentsByParentIdParams> {
  final StudentRepository repository;

  GetStudentsByParentIdUseCase(this.repository);

  @override
  Future<Either<Failure, List<Student>>> call(GetStudentsByParentIdParams params) async {
    return await repository.getStudentsByParentId(params.parentId);
  }
}

/// Get students by parent ID params
class GetStudentsByParentIdParams extends Equatable {
  final String parentId;

  const GetStudentsByParentIdParams({
    required this.parentId,
  });

  @override
  List<Object> get props => [parentId];
}
