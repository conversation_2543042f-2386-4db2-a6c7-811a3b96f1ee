import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';

import '../../core/errors/failures.dart';
import '../../core/usecases/usecase.dart';
import '../entities/student.dart';
import '../repositories/student_repository.dart';

/// Get students use case
class GetStudentsUseCase implements UseCase<List<Student>, GetStudentsParams> {
  final StudentRepository repository;

  GetStudentsUseCase(this.repository);

  @override
  Future<Either<Failure, List<Student>>> call(GetStudentsParams params) async {
    return await repository.getStudents(
      page: params.page,
      perPage: params.perPage,
      search: params.search,
    );
  }
}

/// Get students params
class GetStudentsParams extends Equatable {
  final int page;
  final int perPage;
  final String? search;

  const GetStudentsParams({this.page = 1, this.perPage = 10, this.search});

  @override
  List<Object?> get props => [page, perPage, search];
}
