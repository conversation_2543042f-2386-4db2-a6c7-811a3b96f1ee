import 'package:dartz/dartz.dart';

import '../entities/bus.dart';
import '../repositories/bus_repository.dart';
import '../../core/errors/failures.dart';

/// Update bus use case
/// Following Single Responsibility Principle by focusing only on updating buses
class UpdateBusUseCase {
  final BusRepository _repository;

  UpdateBusUseCase(this._repository);

  Future<Either<Failure, Bus>> call(Bus bus) async {
    return await _repository.updateBus(bus);
  }
}
