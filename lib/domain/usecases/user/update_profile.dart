import 'package:dartz/dartz.dart';
import '../../../core/errors/failures.dart';
import '../../../core/usecases/usecase.dart';
import '../../entities/user.dart';
import '../../repositories/user_repository.dart';

/// UpdateProfile use case
/// Following Single Responsibility Principle by focusing only on updating user profile
class UpdateProfile implements UseCase<User, UpdateProfileParams> {
  final UserRepository repository;

  UpdateProfile(this.repository);

  @override
  Future<Either<Failure, User>> call(UpdateProfileParams params) {
    return repository.updateProfile(params.user);
  }
}

/// UpdateProfileParams for UpdateProfile use case
class UpdateProfileParams {
  final User user;

  UpdateProfileParams({required this.user});
}
