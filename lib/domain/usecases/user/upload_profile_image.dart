import 'package:dartz/dartz.dart';
import '../../../core/errors/failures.dart';
import '../../../core/usecases/usecase.dart';
import '../../repositories/user_repository.dart';

/// UploadProfileImage use case
/// Following Single Responsibility Principle by focusing only on uploading profile image
class UploadProfileImage implements UseCase<String, UploadProfileImageParams> {
  final UserRepository repository;

  UploadProfileImage(this.repository);

  @override
  Future<Either<Failure, String>> call(UploadProfileImageParams params) {
    return repository.uploadProfileImage(params.userId, params.imagePath);
  }
}

/// UploadProfileImageParams for UploadProfileImage use case
class UploadProfileImageParams {
  final String userId;
  final String imagePath;

  UploadProfileImageParams({
    required this.userId,
    required this.imagePath,
  });
}
