import 'package:get/get.dart';

import '../../core/network/api_service.dart';
import '../../data/repositories/bus_repository_impl.dart';
import '../../domain/usecases/create_bus_usecase.dart';
import '../../domain/usecases/delete_bus_usecase.dart';
import '../../domain/usecases/get_buses_usecase.dart';
import '../../domain/usecases/update_bus_usecase.dart';
import '../controllers/buses_controller.dart';

/// Bus binding
/// Following Dependency Inversion Principle by managing dependencies
class BusBinding extends Bindings {
  @override
  void dependencies() {
    // Repository
    Get.lazyPut<BusRepositoryImpl>(
      () => BusRepositoryImpl(Get.find<ApiService>()),
    );

    // Use cases
    Get.lazyPut<GetBusesUseCase>(
      () => GetBusesUseCase(Get.find<BusRepositoryImpl>()),
    );

    Get.lazyPut<CreateBusUseCase>(
      () => CreateBusUseCase(Get.find<BusRepositoryImpl>()),
    );

    Get.lazyPut<UpdateBusUseCase>(
      () => UpdateBusUseCase(Get.find<BusRepositoryImpl>()),
    );

    Get.lazyPut<DeleteBusUseCase>(
      () => DeleteBusUseCase(Get.find<BusRepositoryImpl>()),
    );

    // Controller
    Get.lazyPut<BusesController>(
      () => BusesController(
        getBusesUseCase: Get.find<GetBusesUseCase>(),
        createBusUseCase: Get.find<CreateBusUseCase>(),
        updateBusUseCase: Get.find<UpdateBusUseCase>(),
        deleteBusUseCase: Get.find<DeleteBusUseCase>(),
      ),
      fenix: true,
    );
  }
}
