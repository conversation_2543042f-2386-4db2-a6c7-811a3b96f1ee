import 'package:get/get.dart';

import '../../domain/repositories/ad_repository.dart';
import '../../domain/repositories/trip_repository.dart';
import '../../domain/repositories/user_repository.dart';
import '../../domain/usecases/get_ads.dart';
import '../../domain/usecases/get_current_trip.dart';
import '../../domain/usecases/get_profile.dart';
import '../controllers/home_controller.dart';

/// HomeBinding class for dependency injection
/// Following Dependency Inversion Principle by providing abstractions
class HomeBinding extends Bindings {
  @override
  void dependencies() {
    // Register use cases if not already registered
    if (!Get.isRegistered<GetAds>() && Get.isRegistered<AdRepository>()) {
      Get.lazyPut<GetAds>(() => GetAds(Get.find<AdRepository>()));
    }

    if (!Get.isRegistered<GetCurrentTrip>() &&
        Get.isRegistered<TripRepository>()) {
      Get.lazyPut<GetCurrentTrip>(
        () => GetCurrentTrip(Get.find<TripRepository>()),
      );
    }

    if (!Get.isRegistered<GetProfile>() && Get.isRegistered<UserRepository>()) {
      Get.lazyPut<GetProfile>(() => GetProfile(Get.find<UserRepository>()));
    }

    // Register controller
    Get.lazyPut<HomeController>(
      () => HomeController(
        getAdsUseCase: Get.isRegistered<GetAds>() ? Get.find<GetAds>() : null,
        getCurrentTripUseCase:
            Get.isRegistered<GetCurrentTrip>()
                ? Get.find<GetCurrentTrip>()
                : null,
        getProfileUseCase:
            Get.isRegistered<GetProfile>() ? Get.find<GetProfile>() : null,
      ),
      fenix: true,
    );
  }
}
