import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../core/network/api_service.dart';
import '../../data/datasources/user_local_data_source.dart';
import '../../data/datasources/user_local_data_source_impl.dart';
import '../../data/datasources/user_remote_data_source.dart';
import '../../data/datasources/user_remote_data_source_impl.dart';
import '../../data/repositories/user_repository_impl.dart';
import '../../domain/repositories/user_repository.dart';
import '../../domain/usecases/auth/change_password.dart';
import '../../domain/usecases/auth/forgot_password.dart';
import '../../domain/usecases/auth/get_current_user.dart';
import '../../domain/usecases/auth/is_authenticated.dart';
import '../../domain/usecases/auth/login_user.dart';
import '../../domain/usecases/auth/logout_user.dart';
import '../../domain/usecases/auth/register_user.dart';
import '../../domain/usecases/auth/reset_password.dart';
import '../../domain/usecases/user/update_profile.dart';
import '../../domain/usecases/user/upload_profile_image.dart';
import '../controllers/auth_controller.dart';
import '../controllers/profile_controller.dart';

/// MainBinding for initializing all dependencies
/// This binding should be initialized at app startup
class MainBinding extends Bindings {
  @override
  void dependencies() {
    // Initialize API Service
    final apiService = ApiService();
    Get.put(apiService.init(), permanent: true);

    // Initialize SharedPreferences
    SharedPreferences.getInstance().then((prefs) {
      Get.put(prefs, permanent: true);

      // Initialize data sources
      final remoteDataSource = UserRemoteDataSourceImpl(Get.find<ApiService>());
      Get.put<UserRemoteDataSource>(
        remoteDataSource,
        permanent: true,
        tag: 'remote_data_source',
      );

      final localDataSource = UserLocalDataSourceImpl(sharedPreferences: prefs);
      Get.put<UserLocalDataSource>(
        localDataSource,
        permanent: true,
        tag: 'local_data_source',
      );

      // Initialize repository
      Get.put<UserRepository>(
        UserRepositoryImpl(
          remoteDataSource: Get.find<UserRemoteDataSource>(
            tag: 'remote_data_source',
          ),
          localDataSource: Get.find<UserLocalDataSource>(
            tag: 'local_data_source',
          ),
        ),
        permanent: true,
      );

      // Initialize use cases
      Get.put(GetCurrentUser(Get.find<UserRepository>()), permanent: true);
      Get.put(LoginUser(Get.find<UserRepository>()), permanent: true);
      Get.put(LogoutUser(Get.find<UserRepository>()), permanent: true);
      Get.put(RegisterUser(Get.find<UserRepository>()), permanent: true);
      Get.put(ForgotPassword(Get.find<UserRepository>()), permanent: true);
      Get.put(ResetPassword(Get.find<UserRepository>()), permanent: true);
      Get.put(IsAuthenticated(Get.find<UserRepository>()), permanent: true);
      Get.put(ChangePassword(Get.find<UserRepository>()), permanent: true);
      Get.put(UpdateProfile(Get.find<UserRepository>()), permanent: true);
      Get.put(UploadProfileImage(Get.find<UserRepository>()), permanent: true);

      // Initialize controllers
      Get.put(
        AuthController(
          getCurrentUserUseCase: Get.find<GetCurrentUser>(),
          loginUserUseCase: Get.find<LoginUser>(),
          logoutUserUseCase: Get.find<LogoutUser>(),
          registerUserUseCase: Get.find<RegisterUser>(),
          forgotPasswordUseCase: Get.find<ForgotPassword>(),
          resetPasswordUseCase: Get.find<ResetPassword>(),
          isAuthenticatedUseCase: Get.find<IsAuthenticated>(),
          changePasswordUseCase: Get.find<ChangePassword>(),
        ),
        permanent: true,
      );

      Get.put(
        ProfileController(
          updateProfileUseCase: Get.find<UpdateProfile>(),
          uploadProfileImageUseCase: Get.find<UploadProfileImage>(),
        ),
        permanent: true,
      );
    });
  }
}
