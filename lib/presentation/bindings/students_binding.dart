import 'package:get/get.dart';

import '../../core/network/api_service.dart';
import '../../data/repositories/student_repository_impl.dart';
import '../../domain/usecases/get_students_usecase.dart';
import '../../domain/usecases/get_students_by_bus_id_usecase.dart';
import '../../domain/usecases/get_students_by_parent_id_usecase.dart';
import '../controllers/students_controller.dart';

/// Students binding
class StudentsBinding extends Bindings {
  @override
  void dependencies() {
    // Repository
    Get.lazyPut<StudentRepositoryImpl>(
      () => StudentRepositoryImpl(Get.find<ApiService>()),
    );

    // Use cases
    Get.lazyPut<GetStudentsUseCase>(
      () => GetStudentsUseCase(Get.find<StudentRepositoryImpl>()),
    );

    Get.lazyPut<GetStudentsByBusIdUseCase>(
      () => GetStudentsByBusIdUseCase(Get.find<StudentRepositoryImpl>()),
    );

    Get.lazyPut<GetStudentsByParentIdUseCase>(
      () => GetStudentsByParentIdUseCase(Get.find<StudentRepositoryImpl>()),
    );

    // Controller
    Get.lazyPut<StudentsController>(
      () => StudentsController(
        getStudentsUseCase: Get.find<GetStudentsUseCase>(),
        getStudentsByBusIdUseCase: Get.find<GetStudentsByBusIdUseCase>(),
        getStudentsByParentIdUseCase: Get.find<GetStudentsByParentIdUseCase>(),
      ),
    );
  }
}
