import 'package:get/get.dart';
import '../controllers/supervisors_controller.dart';

/// Supervisors binding for dependency injection
/// Following Single Responsibility Principle by focusing only on supervisors dependencies
class SupervisorsBinding extends Bindings {
  @override
  void dependencies() {
    // The SupervisorsController is already registered in DependencyInjection
    // We just need to ensure it's available when needed
    Get.lazyPut<SupervisorsController>(
      () => Get.find<SupervisorsController>(),
      fenix: true,
    );
  }
}
