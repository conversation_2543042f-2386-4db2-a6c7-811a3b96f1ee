import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../controllers/theme_controller.dart';

/// ThemeBinding for dependency injection
/// Following Dependency Inversion Principle by providing abstractions
class ThemeBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut<ThemeController>(
      () => ThemeController(prefs: Get.find<SharedPreferences>()),
      fenix: true,
    );
  }
}
