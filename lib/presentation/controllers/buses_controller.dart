import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../core/utils/logger.dart';
import '../../domain/entities/bus.dart';

/// Buses controller
/// Following Single Responsibility Principle by focusing only on bus management
class BusesController extends GetxController {
  // Private reactive variables
  final RxList<Bus> _buses = <Bus>[].obs;
  final RxBool _isLoading = false.obs;
  final RxString _errorMessage = ''.obs;
  final RxInt _currentPage = 1.obs;
  final RxInt _lastPage = 1.obs;
  final RxInt _total = 0.obs;
  final RxString _searchQuery = ''.obs;

  // Form controllers
  final GlobalKey<FormState> addBusFormKey = GlobalKey<FormState>();
  final GlobalKey<FormState> editBusFormKey = GlobalKey<FormState>();
  final TextEditingController nameController = TextEditingController();
  final TextEditingController carNumberController = TextEditingController();
  final TextEditingController notesController = TextEditingController();

  // Getters
  List<Bus> get buses => _buses;
  bool get isLoading => _isLoading.value;
  String get errorMessage => _errorMessage.value;
  int get currentPage => _currentPage.value;
  int get lastPage => _lastPage.value;
  int get total => _total.value;
  String get searchQuery => _searchQuery.value;

  @override
  void onInit() {
    super.onInit();
    loadBuses();
  }

  /// Load buses with pagination
  Future<void> loadBuses({int page = 1, bool isRefresh = false}) async {
    if (isRefresh) {
      _currentPage.value = 1;
    } else {
      _currentPage.value = page;
    }

    _isLoading.value = true;
    _errorMessage.value = '';

    try {
      // TODO: Implement actual API call to get buses
      // For now, simulate API call with mock data
      await Future.delayed(const Duration(seconds: 1));

      final mockBuses = _generateMockBuses();

      if (isRefresh) {
        _buses.assignAll(mockBuses);
      } else {
        _buses.addAll(mockBuses);
      }

      _total.value = 25; // Mock total
      _lastPage.value = 3; // Mock last page

      LoggerService.debug('Loaded ${mockBuses.length} buses');
    } catch (e) {
      LoggerService.error('Error loading buses', error: e);
      _errorMessage.value = 'Failed to load buses: ${e.toString()}';
    } finally {
      _isLoading.value = false;
    }
  }

  /// Generate mock buses for demonstration
  List<Bus> _generateMockBuses() {
    return [
      const Bus(
        id: 1,
        name: 'Bus A1',
        carNumber: 'ABC-123',
        notes: 'Morning route - Elementary school',
        createdAt: '2024-01-15T08:00:00Z',
        updatedAt: '2024-01-15T08:00:00Z',
      ),
      const Bus(
        id: 2,
        name: 'Bus B2',
        carNumber: 'DEF-456',
        notes: 'Evening route - Middle school',
        createdAt: '2024-01-16T09:00:00Z',
        updatedAt: '2024-01-16T09:00:00Z',
      ),
      const Bus(
        id: 3,
        name: 'Bus C3',
        carNumber: 'GHI-789',
        notes: 'Full day route - High school',
        createdAt: '2024-01-17T10:00:00Z',
        updatedAt: '2024-01-17T10:00:00Z',
      ),
      const Bus(
        id: 4,
        name: 'Bus D4',
        carNumber: 'JKL-012',
        notes: 'Special needs route',
        createdAt: '2024-01-18T11:00:00Z',
        updatedAt: '2024-01-18T11:00:00Z',
      ),
      const Bus(
        id: 5,
        name: 'Bus E5',
        carNumber: 'MNO-345',
        notes: 'Sports activities route',
        createdAt: '2024-01-19T12:00:00Z',
        updatedAt: '2024-01-19T12:00:00Z',
      ),
    ];
  }

  /// Search buses
  Future<void> searchBuses(String query) async {
    _searchQuery.value = query;

    if (query.isEmpty) {
      await refreshBuses();
      return;
    }

    _isLoading.value = true;
    _errorMessage.value = '';

    try {
      // TODO: Implement actual API call to search buses
      // For now, simulate search with mock data
      await Future.delayed(const Duration(milliseconds: 500));

      final allBuses = _generateMockBuses();
      final filteredBuses = allBuses.where((bus) =>
        bus.name!.toLowerCase().contains(query.toLowerCase()) ||
        bus.carNumber!.toLowerCase().contains(query.toLowerCase()) ||
        (bus.notes?.toLowerCase().contains(query.toLowerCase()) ?? false)
      ).toList();

      _buses.assignAll(filteredBuses);
      _total.value = filteredBuses.length;
      _currentPage.value = 1;
      _lastPage.value = 1;

      LoggerService.debug('Found ${filteredBuses.length} buses for query: $query');
    } catch (e) {
      LoggerService.error('Error searching buses', error: e);
      _errorMessage.value = 'Failed to search buses: ${e.toString()}';
    } finally {
      _isLoading.value = false;
    }
  }

  /// Refresh buses list
  Future<void> refreshBuses() async {
    await loadBuses(isRefresh: true);
  }

  /// Load more buses (pagination)
  Future<void> loadMoreBuses() async {
    if (_currentPage.value < _lastPage.value && !_isLoading.value) {
      await loadBuses(page: _currentPage.value + 1);
    }
  }

  /// Clear form controllers
  void clearFormControllers() {
    nameController.clear();
    carNumberController.clear();
    notesController.clear();
  }

  /// Initialize edit form with bus data
  void initializeEditForm(Bus bus) {
    nameController.text = bus.name ?? '';
    carNumberController.text = bus.carNumber ?? '';
    notesController.text = bus.notes ?? '';
  }

  /// Add new bus
  Future<void> addBus() async {
    if (!addBusFormKey.currentState!.validate()) {
      return;
    }

    _isLoading.value = true;

    try {
      // TODO: Implement actual API call to add bus
      // For now, simulate API call
      await Future.delayed(const Duration(seconds: 2));

      // Show success message
      Get.snackbar(
        'Success',
        'Bus added successfully',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green,
        colorText: Colors.white,
        margin: const EdgeInsets.all(16),
        borderRadius: 12,
      );

      // Clear form and go back
      clearFormControllers();
      Get.back();

      // Refresh buses list
      await refreshBuses();
    } catch (e) {
      LoggerService.error('Error adding bus', error: e);
      Get.snackbar(
        'Error',
        'Failed to add bus: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
        margin: const EdgeInsets.all(16),
        borderRadius: 12,
      );
    } finally {
      _isLoading.value = false;
    }
  }

  /// Update existing bus
  Future<void> updateBus(int? busId) async {
    if (!editBusFormKey.currentState!.validate()) {
      return;
    }

    if (busId == null) {
      Get.snackbar(
        'Error',
        'Bus ID is required for update',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
        margin: const EdgeInsets.all(16),
        borderRadius: 12,
      );
      return;
    }

    _isLoading.value = true;

    try {
      // TODO: Implement actual API call to update bus
      // For now, simulate API call
      await Future.delayed(const Duration(seconds: 2));

      // Show success message
      Get.snackbar(
        'Success',
        'Bus updated successfully',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green,
        colorText: Colors.white,
        margin: const EdgeInsets.all(16),
        borderRadius: 12,
      );

      // Clear form and go back
      clearFormControllers();
      Get.back();

      // Refresh buses list
      await refreshBuses();
    } catch (e) {
      LoggerService.error('Error updating bus', error: e);
      Get.snackbar(
        'Error',
        'Failed to update bus: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
        margin: const EdgeInsets.all(16),
        borderRadius: 12,
      );
    } finally {
      _isLoading.value = false;
    }
  }

  /// Delete bus
  Future<void> deleteBus(int busId) async {
    try {
      // TODO: Implement actual API call to delete bus
      // For now, simulate API call
      await Future.delayed(const Duration(seconds: 1));

      // Remove from local list
      _buses.removeWhere((bus) => bus.id == busId);
      _total.value = _buses.length;

      Get.snackbar(
        'Success',
        'Bus deleted successfully',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green,
        colorText: Colors.white,
        margin: const EdgeInsets.all(16),
        borderRadius: 12,
      );

      LoggerService.debug('Deleted bus with ID: $busId');
    } catch (e) {
      LoggerService.error('Error deleting bus', error: e);
      Get.snackbar(
        'Error',
        'Failed to delete bus: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
        margin: const EdgeInsets.all(16),
        borderRadius: 12,
      );
    }
  }

  @override
  void onClose() {
    // Dispose controllers
    nameController.dispose();
    carNumberController.dispose();
    notesController.dispose();
    super.onClose();
  }
}
