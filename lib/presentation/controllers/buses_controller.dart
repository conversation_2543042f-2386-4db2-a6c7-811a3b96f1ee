import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../core/utils/logger.dart';
import '../../data/models/bus_model.dart';
import '../../domain/entities/bus.dart';
import '../../domain/usecases/create_bus_usecase.dart';
import '../../domain/usecases/delete_bus_usecase.dart';
import '../../domain/usecases/get_buses_usecase.dart';
import '../../domain/usecases/update_bus_usecase.dart';

/// Buses controller
/// Following Single Responsibility Principle by focusing only on bus management
class Bus<PERSON><PERSON>ontroller extends GetxController {
  // Use cases
  final GetBusesUseCase _getBusesUseCase;
  final CreateBusUseCase _createBusUseCase;
  final UpdateBusUseCase _updateBusUseCase;
  final DeleteBusUseCase _deleteBusUseCase;

  BusesController({
    required GetBusesUseCase getBusesUseCase,
    required CreateBusUseCase createBusUseCase,
    required UpdateBusUseCase updateBusUseCase,
    required DeleteBusUseCase deleteBusUseCase,
  })  : _getBusesUseCase = getBusesUseCase,
        _createBusUseCase = createBusUseCase,
        _updateBusUseCase = updateBusUseCase,
        _deleteBusUseCase = deleteBusUseCase;
  // Private reactive variables
  final RxList<Bus> _buses = <Bus>[].obs;
  final RxBool _isLoading = false.obs;
  final RxString _errorMessage = ''.obs;
  final RxInt _currentPage = 1.obs;
  final RxInt _lastPage = 1.obs;
  final RxInt _total = 0.obs;
  final RxString _searchQuery = ''.obs;

  // Form controllers
  final GlobalKey<FormState> addBusFormKey = GlobalKey<FormState>();
  final GlobalKey<FormState> editBusFormKey = GlobalKey<FormState>();
  final TextEditingController nameController = TextEditingController();
  final TextEditingController carNumberController = TextEditingController();
  final TextEditingController notesController = TextEditingController();

  // Getters
  List<Bus> get buses => _buses;
  bool get isLoading => _isLoading.value;
  String get errorMessage => _errorMessage.value;
  int get currentPage => _currentPage.value;
  int get lastPage => _lastPage.value;
  int get total => _total.value;
  String get searchQuery => _searchQuery.value;

  @override
  void onInit() {
    super.onInit();
    loadBuses();
  }

  /// Load buses with pagination
  Future<void> loadBuses({int page = 1, bool isRefresh = false}) async {
    if (isRefresh) {
      _currentPage.value = 1;
      page = 1;
    } else {
      _currentPage.value = page;
    }

    _isLoading.value = true;
    _errorMessage.value = '';

    try {
      // Call real API to get buses
      final result = await _getBusesUseCase.call(
        page: page,
        perPage: 10,
        search: _searchQuery.value.isNotEmpty ? _searchQuery.value : null,
      );

      result.fold(
        (failure) {
          LoggerService.error('Failed to load buses', data: {'error': failure.message});
          _errorMessage.value = failure.message;
        },
        (buses) {
          if (isRefresh) {
            _buses.assignAll(buses);
          } else {
            _buses.addAll(buses);
          }

          // Update pagination info (these would come from API response in real implementation)
          _total.value = buses.length;
          _lastPage.value = (buses.length / 10).ceil();

          LoggerService.debug('Loaded ${buses.length} buses from API');
        },
      );
    } catch (e) {
      LoggerService.error('Error loading buses', error: e);
      _errorMessage.value = 'Failed to load buses: ${e.toString()}';
    } finally {
      _isLoading.value = false;
    }
  }

  /// Search buses
  Future<void> searchBuses(String query) async {
    _searchQuery.value = query;

    // Reset to first page and reload with search
    await loadBuses(page: 1, isRefresh: true);
  }

  /// Refresh buses list
  Future<void> refreshBuses() async {
    await loadBuses(isRefresh: true);
  }

  /// Load more buses (pagination)
  Future<void> loadMoreBuses() async {
    if (_currentPage.value < _lastPage.value && !_isLoading.value) {
      await loadBuses(page: _currentPage.value + 1);
    }
  }

  /// Clear form controllers
  void clearFormControllers() {
    nameController.clear();
    carNumberController.clear();
    notesController.clear();
  }

  /// Initialize edit form with bus data
  void initializeEditForm(Bus bus) {
    nameController.text = bus.name ?? '';
    carNumberController.text = bus.carNumber ?? '';
    notesController.text = bus.notes ?? '';
  }

  /// Add new bus
  Future<void> addBus() async {
    if (!addBusFormKey.currentState!.validate()) {
      return;
    }

    _isLoading.value = true;

    try {
      // Create bus model from form data
      final busModel = BusModel(
        name: nameController.text.trim(),
        carNumber: carNumberController.text.trim(),
        notes: notesController.text.trim().isNotEmpty ? notesController.text.trim() : null,
      );

      // Call real API to create bus
      final result = await _createBusUseCase.call(busModel);

      result.fold(
        (failure) {
          LoggerService.error('Failed to create bus', data: {'error': failure.message});
          Get.snackbar(
            'Error',
            failure.message,
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: Colors.red,
            colorText: Colors.white,
            margin: const EdgeInsets.all(16),
            borderRadius: 12,
          );
        },
        (createdBus) {
          LoggerService.info('Bus created successfully', data: {'busId': createdBus.id});

          // Show success message
          Get.snackbar(
            'Success',
            'Bus added successfully',
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: Colors.green,
            colorText: Colors.white,
            margin: const EdgeInsets.all(16),
            borderRadius: 12,
          );

          // Clear form and go back
          clearFormControllers();
          Get.back();

          // Refresh buses list
          refreshBuses();
        },
      );
    } catch (e) {
      LoggerService.error('Error adding bus', error: e);
      Get.snackbar(
        'Error',
        'Failed to add bus: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
        margin: const EdgeInsets.all(16),
        borderRadius: 12,
      );
    } finally {
      _isLoading.value = false;
    }
  }

  /// Update existing bus
  Future<void> updateBus(int? busId) async {
    if (!editBusFormKey.currentState!.validate()) {
      return;
    }

    if (busId == null) {
      Get.snackbar(
        'Error',
        'Bus ID is required for update',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
        margin: const EdgeInsets.all(16),
        borderRadius: 12,
      );
      return;
    }

    _isLoading.value = true;

    try {
      // Create bus model from form data
      final busModel = BusModel(
        id: busId,
        name: nameController.text.trim(),
        carNumber: carNumberController.text.trim(),
        notes: notesController.text.trim().isNotEmpty ? notesController.text.trim() : null,
      );

      // Call real API to update bus
      final result = await _updateBusUseCase.call(busModel);

      result.fold(
        (failure) {
          LoggerService.error('Failed to update bus', data: {'error': failure.message});
          Get.snackbar(
            'Error',
            failure.message,
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: Colors.red,
            colorText: Colors.white,
            margin: const EdgeInsets.all(16),
            borderRadius: 12,
          );
        },
        (updatedBus) {
          LoggerService.info('Bus updated successfully', data: {'busId': updatedBus.id});

          // Show success message
          Get.snackbar(
            'Success',
            'Bus updated successfully',
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: Colors.green,
            colorText: Colors.white,
            margin: const EdgeInsets.all(16),
            borderRadius: 12,
          );

          // Clear form and go back
          clearFormControllers();
          Get.back();

          // Refresh buses list
          refreshBuses();
        },
      );
    } catch (e) {
      LoggerService.error('Error updating bus', error: e);
      Get.snackbar(
        'Error',
        'Failed to update bus: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
        margin: const EdgeInsets.all(16),
        borderRadius: 12,
      );
    } finally {
      _isLoading.value = false;
    }
  }

  /// Delete bus
  Future<void> deleteBus(int busId) async {
    try {
      // Call real API to delete bus
      final result = await _deleteBusUseCase.call(busId);

      result.fold(
        (failure) {
          LoggerService.error('Failed to delete bus', data: {'error': failure.message});
          Get.snackbar(
            'Error',
            failure.message,
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: Colors.red,
            colorText: Colors.white,
            margin: const EdgeInsets.all(16),
            borderRadius: 12,
          );
        },
        (success) {
          LoggerService.info('Bus deleted successfully', data: {'busId': busId});

          // Remove from local list
          _buses.removeWhere((bus) => bus.id == busId);
          _total.value = _buses.length;

          Get.snackbar(
            'Success',
            'Bus deleted successfully',
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: Colors.green,
            colorText: Colors.white,
            margin: const EdgeInsets.all(16),
            borderRadius: 12,
          );
        },
      );
    } catch (e) {
      LoggerService.error('Error deleting bus', error: e);
      Get.snackbar(
        'Error',
        'Failed to delete bus: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
        margin: const EdgeInsets.all(16),
        borderRadius: 12,
      );
    }
  }

  @override
  void onClose() {
    // Dispose controllers
    nameController.dispose();
    carNumberController.dispose();
    notesController.dispose();
    super.onClose();
  }
}
