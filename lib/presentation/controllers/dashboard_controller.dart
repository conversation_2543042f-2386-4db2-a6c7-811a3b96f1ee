import 'package:get/get.dart';
import '../../core/utils/logger.dart';
import '../../domain/entities/school_stats.dart';
import '../../domain/entities/trip.dart';
import '../../domain/usecases/dashboard/get_dashboard_stats.dart';
import '../../domain/usecases/dashboard/get_recent_trips.dart';
import '../../domain/usecases/dashboard/get_school_stats.dart';
import '../../domain/usecases/dashboard/get_upcoming_trips.dart';
import '../routes/app_routes.dart';

/// DashboardController class for managing dashboard state
/// Following Single Responsibility Principle by focusing only on dashboard logic
class DashboardController extends GetxController {
  // Use cases
  final GetDashboardStats getDashboardStatsUseCase;
  final GetSchoolStats getSchoolStatsUseCase;
  final GetRecentTrips getRecentTripsUseCase;
  final GetUpcomingTrips getUpcomingTripsUseCase;
  
  // Observable state
  final _isLoading = true.obs;
  final _errorMessage = ''.obs;
  final _totalStudents = 0.obs;
  final _totalBuses = 0.obs;
  final _totalSupervisors = 0.obs;
  final _totalDrivers = 0.obs;
  final _totalTrips = 0.obs;
  final _schoolStats = <SchoolStats>[].obs;
  final _recentTrips = <Trip>[].obs;
  final _upcomingTrips = <Trip>[].obs;

  // Getters
  bool get isLoading => _isLoading.value;
  String get errorMessage => _errorMessage.value;
  int get totalStudents => _totalStudents.value;
  int get totalBuses => _totalBuses.value;
  int get totalSupervisors => _totalSupervisors.value;
  int get totalDrivers => _totalDrivers.value;
  int get totalTrips => _totalTrips.value;
  List<SchoolStats> get schoolStats => _schoolStats;
  List<Trip> get recentTrips => _recentTrips;
  List<Trip> get upcomingTrips => _upcomingTrips;
  
  DashboardController({
    required this.getDashboardStatsUseCase,
    required this.getSchoolStatsUseCase,
    required this.getRecentTripsUseCase,
    required this.getUpcomingTripsUseCase,
  });
  
  @override
  void onInit() {
    super.onInit();
    loadDashboardData();
  }
  
  /// Load dashboard data
  Future<void> loadDashboardData() async {
    _isLoading.value = true;
    _errorMessage.value = '';
    
    try {
      // Load dashboard stats
      await _loadDashboardStats();
      
      // Load school stats
      await _loadSchoolStats();
      
      // Load recent trips
      await _loadRecentTrips();
      
      // Load upcoming trips
      await _loadUpcomingTrips();
    } catch (e) {
      LoggerService.error('Error loading dashboard data', error: e);
      _errorMessage.value = 'Failed to load dashboard data';
    } finally {
      _isLoading.value = false;
    }
  }
  
  /// Load dashboard stats
  Future<void> _loadDashboardStats() async {
    final result = await getDashboardStatsUseCase();
    
    result.fold(
      (failure) {
        LoggerService.error('Error loading dashboard stats', error: failure);
        _errorMessage.value = failure.message;
      },
      (stats) {
        _totalStudents.value = stats.totalStudents;
        _totalBuses.value = stats.totalBuses;
        _totalSupervisors.value = stats.totalSupervisors;
        _totalDrivers.value = stats.totalDrivers;
        _totalTrips.value = stats.totalTrips;
      },
    );
  }
  
  /// Load school stats
  Future<void> _loadSchoolStats() async {
    final result = await getSchoolStatsUseCase();
    
    result.fold(
      (failure) {
        LoggerService.error('Error loading school stats', error: failure);
        _errorMessage.value = failure.message;
      },
      (stats) {
        _schoolStats.assignAll(stats);
      },
    );
  }
  
  /// Load recent trips
  Future<void> _loadRecentTrips() async {
    final result = await getRecentTripsUseCase();
    
    result.fold(
      (failure) {
        LoggerService.error('Error loading recent trips', error: failure);
        _errorMessage.value = failure.message;
      },
      (trips) {
        _recentTrips.assignAll(trips);
      },
    );
  }
  
  /// Load upcoming trips
  Future<void> _loadUpcomingTrips() async {
    final result = await getUpcomingTripsUseCase();
    
    result.fold(
      (failure) {
        LoggerService.error('Error loading upcoming trips', error: failure);
        _errorMessage.value = failure.message;
      },
      (trips) {
        _upcomingTrips.assignAll(trips);
      },
    );
  }
  
  /// Refresh dashboard data
  Future<void> refreshDashboardData() async {
    await loadDashboardData();
  }
  
  /// Navigate to students page
  void navigateToStudents() {
    Get.toNamed(AppRoutes.students);
  }

  /// Navigate to buses page
  void navigateToBuses() {
    Get.toNamed(AppRoutes.buses);
  }

  /// Navigate to supervisors page
  void navigateToSupervisors() {
    Get.toNamed(AppRoutes.supervisors);
  }

  /// Navigate to drivers page
  void navigateToDrivers() {
    Get.toNamed(AppRoutes.drivers);
  }

  /// Navigate to trips page
  void navigateToTrips() {
    Get.toNamed(AppRoutes.trips);
  }
}
