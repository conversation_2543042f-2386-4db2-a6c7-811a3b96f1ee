import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../core/utils/logger.dart';
import '../../domain/entities/driver.dart';
import '../../domain/repositories/driver_repository.dart';
import '../../data/models/driver_model.dart';
import '../routes/app_routes.dart';

/// DriversController class for managing drivers state
/// Following Single Responsibility Principle by focusing only on drivers logic
class DriversController extends GetxController {
  final DriverRepository _driverRepository;

  DriversController({required DriverRepository driverRepository})
      : _driverRepository = driverRepository;

  // Observable state
  final _isLoading = false.obs;
  final _isLoadingMore = false.obs;
  final _errorMessage = ''.obs;
  final _drivers = <Driver>[].obs;
  final _filteredDrivers = <Driver>[].obs;
  final _searchQuery = ''.obs;
  final _currentPage = 1.obs;
  final _hasMoreData = true.obs;
  final _selectedDriver = Rxn<Driver>();

  // Form state
  final _availableBuses = <Map<String, dynamic>>[].obs;
  final _genderOptions = <Map<String, dynamic>>[].obs;
  final _religionOptions = <Map<String, dynamic>>[].obs;
  final _bloodTypeOptions = <Map<String, dynamic>>[].obs;

  // Getters
  bool get isLoading => _isLoading.value;
  bool get isLoadingMore => _isLoadingMore.value;
  String get errorMessage => _errorMessage.value;
  List<Driver> get drivers => _filteredDrivers.isEmpty && _searchQuery.isEmpty
      ? _drivers
      : _filteredDrivers;
  String get searchQuery => _searchQuery.value;
  int get currentPage => _currentPage.value;
  bool get hasMoreData => _hasMoreData.value;
  Driver? get selectedDriver => _selectedDriver.value;

  // Form options
  List<Map<String, dynamic>> get availableBuses => _availableBuses;
  List<Map<String, dynamic>> get genderOptions => _genderOptions;
  List<Map<String, dynamic>> get religionOptions => _religionOptions;
  List<Map<String, dynamic>> get bloodTypeOptions => _bloodTypeOptions;

  @override
  void onInit() {
    super.onInit();
    loadDrivers();
    loadFormOptions();
  }

  /// Load drivers with pagination
  Future<void> loadDrivers({bool refresh = false}) async {
    if (refresh) {
      _currentPage.value = 1;
      _hasMoreData.value = true;
      _drivers.clear();
      _filteredDrivers.clear();
    }

    if (_isLoading.value || (!_hasMoreData.value && !refresh)) return;

    _isLoading.value = refresh || _drivers.isEmpty;
    _isLoadingMore.value = !refresh && _drivers.isNotEmpty;
    _errorMessage.value = '';

    try {
      final result = await _driverRepository.getDrivers(
        page: _currentPage.value,
        limit: 10,
        search: _searchQuery.value.isNotEmpty ? _searchQuery.value : null,
      );

      result.fold(
        (failure) {
          LoggerService.error('Error loading drivers', error: failure);
          _errorMessage.value = failure.toString();
        },
        (newDrivers) {
          if (refresh) {
            _drivers.assignAll(newDrivers);
          } else {
            _drivers.addAll(newDrivers);
          }

          _hasMoreData.value = newDrivers.length >= 10;
          if (_hasMoreData.value) {
            _currentPage.value++;
          }

          _filterDrivers();
        },
      );
    } catch (e) {
      LoggerService.error('Unexpected error loading drivers', error: e);
      _errorMessage.value = e.toString();
    } finally {
      _isLoading.value = false;
      _isLoadingMore.value = false;
    }
  }

  /// Load more drivers (pagination)
  Future<void> loadMoreDrivers() async {
    if (!_hasMoreData.value || _isLoadingMore.value) return;
    await loadDrivers();
  }

  /// Search drivers
  void searchDrivers(String query) {
    _searchQuery.value = query;
    _filterDrivers();

    // If search query is not empty, fetch from server
    if (query.isNotEmpty) {
      _currentPage.value = 1;
      _hasMoreData.value = true;
      loadDrivers(refresh: true);
    }
  }

  /// Filter drivers locally
  void _filterDrivers() {
    if (_searchQuery.value.isEmpty) {
      _filteredDrivers.clear();
      return;
    }

    final query = _searchQuery.value.toLowerCase();
    _filteredDrivers.assignAll(
      _drivers.where((driver) {
        return driver.name?.toLowerCase().contains(query) == true ||
            driver.username?.toLowerCase().contains(query) == true ||
            driver.phone?.toLowerCase().contains(query) == true ||
            driver.busName?.toLowerCase().contains(query) == true;
      }).toList(),
    );
  }

  /// Refresh drivers list
  Future<void> refreshDrivers() async {
    await loadDrivers(refresh: true);
  }

  /// Load form options (buses, genders, religions, blood types)
  Future<void> loadFormOptions() async {
    try {
      // Load available buses
      final busesResult = await _driverRepository.getAvailableBuses();
      busesResult.fold(
        (failure) => LoggerService.error('Error loading buses', error: failure),
        (buses) => _availableBuses.assignAll(buses),
      );

      // Load gender options
      final genderResult = await _driverRepository.getGenderOptions();
      genderResult.fold(
        (failure) => LoggerService.error('Error loading genders', error: failure),
        (genders) => _genderOptions.assignAll(genders),
      );

      // Load religion options
      final religionResult = await _driverRepository.getReligionOptions();
      religionResult.fold(
        (failure) => LoggerService.error('Error loading religions', error: failure),
        (religions) => _religionOptions.assignAll(religions),
      );

      // Load blood type options
      final bloodTypeResult = await _driverRepository.getBloodTypeOptions();
      bloodTypeResult.fold(
        (failure) => LoggerService.error('Error loading blood types', error: failure),
        (bloodTypes) => _bloodTypeOptions.assignAll(bloodTypes),
      );
    } catch (e) {
      LoggerService.error('Error loading form options', error: e);
    }
  }

  /// Get driver by ID
  Future<void> getDriverById(int id) async {
    _isLoading.value = true;
    _errorMessage.value = '';

    try {
      final result = await _driverRepository.getDriverById(id);
      result.fold(
        (failure) {
          LoggerService.error('Error loading driver', error: failure);
          _errorMessage.value = failure.toString();
        },
        (driver) {
          _selectedDriver.value = driver;
        },
      );
    } catch (e) {
      LoggerService.error('Unexpected error loading driver', error: e);
      _errorMessage.value = e.toString();
    } finally {
      _isLoading.value = false;
    }
  }

  /// Create new driver
  Future<bool> createDriver(DriverModel driver, {
    String? password,
    String? passwordConfirmation,
  }) async {
    _isLoading.value = true;
    _errorMessage.value = '';

    try {
      final result = await _driverRepository.createDriver(
        driver,
        password: password,
        passwordConfirmation: passwordConfirmation,
      );

      return result.fold(
        (failure) {
          LoggerService.error('Error creating driver', error: failure);
          _errorMessage.value = failure.toString();
          Get.snackbar(
            'خطأ',
            failure.toString(),
            backgroundColor: Colors.red,
            colorText: Colors.white,
            snackPosition: SnackPosition.TOP,
          );
          return false;
        },
        (createdDriver) {
          LoggerService.info('Driver created successfully');
          _drivers.insert(0, createdDriver);
          _filterDrivers();
          
          Get.snackbar(
            'نجح',
            'تم إنشاء السائق بنجاح',
            backgroundColor: Colors.green,
            colorText: Colors.white,
            snackPosition: SnackPosition.TOP,
          );
          return true;
        },
      );
    } catch (e) {
      LoggerService.error('Unexpected error creating driver', error: e);
      _errorMessage.value = e.toString();
      Get.snackbar(
        'خطأ',
        e.toString(),
        backgroundColor: Colors.red,
        colorText: Colors.white,
        snackPosition: SnackPosition.TOP,
      );
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// Update existing driver
  Future<bool> updateDriver(DriverModel driver, {String? password}) async {
    _isLoading.value = true;
    _errorMessage.value = '';

    try {
      final result = await _driverRepository.updateDriver(
        driver,
        password: password,
      );

      return result.fold(
        (failure) {
          LoggerService.error('Error updating driver', error: failure);
          _errorMessage.value = failure.toString();
          Get.snackbar(
            'خطأ',
            failure.toString(),
            backgroundColor: Colors.red,
            colorText: Colors.white,
            snackPosition: SnackPosition.TOP,
          );
          return false;
        },
        (updatedDriver) {
          LoggerService.info('Driver updated successfully');
          
          // Update driver in the list
          final index = _drivers.indexWhere((d) => d.id == updatedDriver.id);
          if (index != -1) {
            _drivers[index] = updatedDriver;
            _filterDrivers();
          }
          
          _selectedDriver.value = updatedDriver;
          
          Get.snackbar(
            'نجح',
            'تم تحديث السائق بنجاح',
            backgroundColor: Colors.green,
            colorText: Colors.white,
            snackPosition: SnackPosition.TOP,
          );
          return true;
        },
      );
    } catch (e) {
      LoggerService.error('Unexpected error updating driver', error: e);
      _errorMessage.value = e.toString();
      Get.snackbar(
        'خطأ',
        e.toString(),
        backgroundColor: Colors.red,
        colorText: Colors.white,
        snackPosition: SnackPosition.TOP,
      );
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// Delete driver with enhanced error handling
  Future<bool> deleteDriver(int id) async {
    _isLoading.value = true;
    _errorMessage.value = '';

    try {
      // Find the driver to get name for confirmation
      final driver = _drivers.firstWhereOrNull((d) => d.id == id);
      final driverName = driver?.name ?? 'السائق';

      final result = await _driverRepository.deleteDriver(id);

      return result.fold(
        (failure) {
          LoggerService.error('Error deleting driver', error: failure);
          _errorMessage.value = failure.message;

          // Show detailed error message
          Get.snackbar(
            'فشل في حذف السائق',
            'لم يتم حذف $driverName: ${failure.message}',
            backgroundColor: Colors.red,
            colorText: Colors.white,
            snackPosition: SnackPosition.TOP,
            duration: const Duration(seconds: 4),
            icon: const Icon(Icons.error, color: Colors.white),
          );
          return false;
        },
        (success) {
          LoggerService.info('Driver deleted successfully', data: {'driverId': id, 'driverName': driverName});

          // Remove driver from the list
          _drivers.removeWhere((d) => d.id == id);
          _filteredDrivers.removeWhere((d) => d.id == id);

          // Clear selected driver if it was the deleted one
          if (_selectedDriver.value?.id == id) {
            _selectedDriver.value = null;
          }

          // Show success message
          Get.snackbar(
            'تم الحذف بنجاح',
            'تم حذف السائق $driverName بنجاح',
            backgroundColor: Colors.green,
            colorText: Colors.white,
            snackPosition: SnackPosition.TOP,
            duration: const Duration(seconds: 3),
            icon: const Icon(Icons.check_circle, color: Colors.white),
          );
          return true;
        },
      );
    } catch (e) {
      LoggerService.error('Unexpected error deleting driver', error: e);
      _errorMessage.value = e.toString();

      Get.snackbar(
        'خطأ غير متوقع',
        'حدث خطأ أثناء حذف السائق: ${e.toString()}',
        backgroundColor: Colors.red,
        colorText: Colors.white,
        snackPosition: SnackPosition.TOP,
        duration: const Duration(seconds: 4),
        icon: const Icon(Icons.error, color: Colors.white),
      );
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// Navigate to add driver page
  void navigateToAddDriver() {
    Get.toNamed(AppRoutes.addDriver);
  }

  /// Navigate to edit driver page
  void navigateToEditDriver(Driver driver) {
    _selectedDriver.value = driver;
    Get.toNamed(AppRoutes.editDriver, arguments: driver);
  }

  /// Navigate to driver details page
  void navigateToDriverDetails(Driver driver) {
    _selectedDriver.value = driver;
    Get.toNamed(AppRoutes.driverDetails, arguments: driver);
  }

  /// Clear selected driver
  void clearSelectedDriver() {
    _selectedDriver.value = null;
  }

  /// Clear error message
  void clearError() {
    _errorMessage.value = '';
  }
}
