import 'package:get/get.dart';
import '../../core/utils/logger.dart';
import '../../domain/entities/user.dart';
import '../../domain/usecases/user/update_profile.dart';
import '../../domain/usecases/user/upload_profile_image.dart';

/// ProfileController class for managing profile operations
/// Following Single Responsibility Principle by focusing only on profile operations
class ProfileController extends GetxController {
  final UpdateProfile updateProfileUseCase;
  final UploadProfileImage? uploadProfileImageUseCase;

  ProfileController({
    required this.updateProfileUseCase,
    this.uploadProfileImageUseCase,
  });

  // Observable state
  final RxBool _isLoading = false.obs;
  final RxString _errorMessage = ''.obs;
  final RxString _successMessage = ''.obs;

  // Getters
  bool get isLoading => _isLoading.value;
  String get errorMessage => _errorMessage.value;
  String get successMessage => _successMessage.value;

  /// Update user profile
  Future<bool> updateProfile(User user) async {
    LoggerService.info('Updating profile for user: ${user.id}');
    _isLoading.value = true;
    _errorMessage.value = '';
    _successMessage.value = '';

    try {
      final params = UpdateProfileParams(user: user);
      LoggerService.debug(
        'Calling updateProfileUseCase with params',
        data: {'userId': user.id, 'name': user.name},
      );

      final result = await updateProfileUseCase(params);

      return result.fold(
        (failure) {
          LoggerService.error('Profile update failed', error: failure.message);
          _errorMessage.value = failure.message;
          _isLoading.value = false;
          return false;
        },
        (updatedUser) {
          LoggerService.success(
            'Profile update successful',
            data: {'userId': updatedUser.id, 'name': updatedUser.name},
          );
          _successMessage.value = 'Profile updated successfully';
          _isLoading.value = false;
          return true;
        },
      );
    } catch (e, stackTrace) {
      LoggerService.error(
        'Unexpected error during profile update',
        error: e,
        stackTrace: stackTrace,
      );
      _errorMessage.value = 'An unexpected error occurred: ${e.toString()}';
      _isLoading.value = false;
      return false;
    }
  }

  /// Upload profile image
  Future<bool> uploadProfileImage(String userId, String imagePath) async {
    if (uploadProfileImageUseCase == null) {
      _errorMessage.value = 'Image upload not supported';
      return false;
    }

    LoggerService.info('Uploading profile image for user: $userId');
    _isLoading.value = true;
    _errorMessage.value = '';
    _successMessage.value = '';

    try {
      final params = UploadProfileImageParams(
        userId: userId,
        imagePath: imagePath,
      );
      LoggerService.debug(
        'Calling uploadProfileImageUseCase with params',
        data: {'userId': userId, 'imagePath': imagePath},
      );

      final result = await uploadProfileImageUseCase!(params);

      return result.fold(
        (failure) {
          LoggerService.error('Image upload failed', error: failure.message);
          _errorMessage.value = failure.message;
          _isLoading.value = false;
          return false;
        },
        (imageUrl) {
          LoggerService.success(
            'Image upload successful',
            data: {'userId': userId, 'imageUrl': imageUrl},
          );
          _successMessage.value = 'Profile image updated successfully';
          _isLoading.value = false;
          return true;
        },
      );
    } catch (e, stackTrace) {
      LoggerService.error(
        'Unexpected error during image upload',
        error: e,
        stackTrace: stackTrace,
      );
      _errorMessage.value = 'An unexpected error occurred: ${e.toString()}';
      _isLoading.value = false;
      return false;
    }
  }
}
