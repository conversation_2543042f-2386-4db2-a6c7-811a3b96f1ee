import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:file_picker/file_picker.dart';

import '../../core/services/excel_service.dart';
import '../../core/utils/logger.dart';
import '../../domain/entities/student.dart';
import '../../domain/usecases/get_students_usecase.dart';
import '../../domain/usecases/get_students_by_bus_id_usecase.dart';
import '../../domain/usecases/get_students_by_parent_id_usecase.dart';

/// Students controller
class StudentsController extends GetxController {
  final GetStudentsUseCase getStudentsUseCase;
  final GetStudentsByBusIdUseCase getStudentsByBusIdUseCase;
  final GetStudentsByParentIdUseCase getStudentsByParentIdUseCase;

  StudentsController({
    required this.getStudentsUseCase,
    required this.getStudentsByBusIdUseCase,
    required this.getStudentsByParentIdUseCase,
  });

  // Observable state
  final RxList<Student> _students = <Student>[].obs;
  final RxBool _isLoading = false.obs;
  final RxString _errorMessage = ''.obs;
  final RxInt _currentPage = 1.obs;
  final RxInt _lastPage = 1.obs;
  final RxInt _total = 0.obs;
  final RxString _searchQuery = ''.obs;

  // Form controllers for add/edit student
  final GlobalKey<FormState> addStudentFormKey = GlobalKey<FormState>();
  final GlobalKey<FormState> editStudentFormKey = GlobalKey<FormState>();

  final TextEditingController nameController = TextEditingController();
  final TextEditingController phoneController = TextEditingController();
  final TextEditingController addressController = TextEditingController();
  final TextEditingController cityNameController = TextEditingController();
  final TextEditingController dateBirthController = TextEditingController();
  final TextEditingController genderController = TextEditingController();
  final TextEditingController religionController = TextEditingController();
  final TextEditingController gradeController = TextEditingController();
  final TextEditingController classroomController = TextEditingController();
  final TextEditingController busController = TextEditingController();
  final TextEditingController typeBloodController = TextEditingController();
  final TextEditingController tripTypeController = TextEditingController();

  // Selected IDs for form submission
  final RxnInt selectedGenderId = RxnInt();
  final RxnInt selectedReligionId = RxnInt();
  final RxnInt selectedGradeId = RxnInt();
  final RxnInt selectedClassroomId = RxnInt();
  final RxnInt selectedBusId = RxnInt();
  final RxnInt selectedTypeBloodId = RxnInt();

  // Import functionality
  final RxBool _isImporting = false.obs;
  final RxString _importProgress = ''.obs;
  final RxInt _importedCount = 0.obs;
  final RxInt _importErrorCount = 0.obs;

  // Getters
  List<Student> get students => _students;
  bool get isLoading => _isLoading.value;
  String get errorMessage => _errorMessage.value;
  int get currentPage => _currentPage.value;
  int get lastPage => _lastPage.value;
  int get total => _total.value;
  String get searchQuery => _searchQuery.value;

  // Import getters
  bool get isImporting => _isImporting.value;
  String get importProgress => _importProgress.value;
  int get importedCount => _importedCount.value;
  int get importErrorCount => _importErrorCount.value;

  @override
  void onInit() {
    super.onInit();
    loadStudents();
  }

  /// Load students
  Future<void> loadStudents({int page = 1, bool refresh = false}) async {
    _isLoading.value = true;
    _errorMessage.value = '';

    LoggerService.debug('Loading students', data: {
      'page': page,
      'refresh': refresh,
      'searchQuery': _searchQuery.value,
    });

    if (refresh) {
      _students.clear();
      _currentPage.value = 1;
    } else {
      _currentPage.value = page;
    }

    final params = GetStudentsParams(
      page: _currentPage.value,
      perPage: 10,
      search: _searchQuery.value.isNotEmpty ? _searchQuery.value : null,
    );

    LoggerService.debug('Calling getStudentsUseCase with params', data: {
      'page': params.page,
      'perPage': params.perPage,
      'search': params.search,
    });

    final result = await getStudentsUseCase(params);

    result.fold(
      (failure) {
        LoggerService.error('Error loading students', error: failure);
        _errorMessage.value = failure.message;
      },
      (studentsData) {
        // تسجيل البيانات المستلمة
        LoggerService.debug('Students data received', data: {
          'count': studentsData.length,
          'first_student': studentsData.isNotEmpty ? {
            'id': studentsData.first.id,
            'name': studentsData.first.name,
          } : null,
        });

        if (refresh || page == 1) {
          _students.assignAll(studentsData);
        } else {
          _students.addAll(studentsData);
        }

        // تسجيل حالة القائمة بعد التحديث
        LoggerService.debug('Students list after update', data: {
          'count': _students.length,
          'isEmpty': _students.isEmpty,
        });

        // تحديث معلومات الصفحات
        _lastPage.value = 10; // قيمة افتراضية
        _total.value = studentsData.length * 10; // قيمة افتراضية
        _currentPage.value = page;
      },
    );

    _isLoading.value = false;
  }

  /// Search students
  void searchStudents(String query) {
    _searchQuery.value = query;
    loadStudents(refresh: true);
  }

  /// Load next page
  Future<void> loadNextPage() async {
    if (_currentPage.value < _lastPage.value && !_isLoading.value) {
      await loadStudents(page: _currentPage.value + 1);
    }
  }

  /// Refresh students
  Future<void> refreshStudents() async {
    await loadStudents(refresh: true);
  }

  /// Load students by bus ID
  Future<void> loadStudentsByBusId(String busId) async {
    _isLoading.value = true;
    _errorMessage.value = '';

    final params = GetStudentsByBusIdParams(busId: busId);
    final result = await getStudentsByBusIdUseCase(params);

    result.fold(
      (failure) {
        LoggerService.error('Error loading students by bus ID', error: failure);
        _errorMessage.value = failure.message;
      },
      (studentsData) {
        _students.assignAll(studentsData);
      },
    );

    _isLoading.value = false;
  }

  /// Load students by parent ID
  Future<void> loadStudentsByParentId(String parentId) async {
    _isLoading.value = true;
    _errorMessage.value = '';

    final params = GetStudentsByParentIdParams(parentId: parentId);
    final result = await getStudentsByParentIdUseCase(params);

    result.fold(
      (failure) {
        LoggerService.error('Error loading students by parent ID', error: failure);
        _errorMessage.value = failure.message;
      },
      (studentsData) {
        _students.assignAll(studentsData);
      },
    );

    _isLoading.value = false;
  }

  /// Clear form controllers
  void clearFormControllers() {
    nameController.clear();
    phoneController.clear();
    addressController.clear();
    cityNameController.clear();
    dateBirthController.clear();
    genderController.clear();
    religionController.clear();
    gradeController.clear();
    classroomController.clear();
    busController.clear();
    typeBloodController.clear();
    tripTypeController.clear();

    // Clear selected IDs
    selectedGenderId.value = null;
    selectedReligionId.value = null;
    selectedGradeId.value = null;
    selectedClassroomId.value = null;
    selectedBusId.value = null;
    selectedTypeBloodId.value = null;
  }

  /// Initialize edit form with student data
  void initializeEditForm(Student student) {
    nameController.text = student.name ?? '';
    phoneController.text = student.phone ?? '';
    addressController.text = student.address ?? '';
    cityNameController.text = student.cityName ?? '';
    dateBirthController.text = student.dateBirth ?? '';
    genderController.text = student.gender ?? '';
    religionController.text = student.religion ?? '';
    gradeController.text = student.grade?.name ?? '';
    classroomController.text = student.classroom ?? '';
    busController.text = student.bus?.name ?? '';
    typeBloodController.text = student.typeBlood?.name ?? '';
    tripTypeController.text = student.tripType ?? '';

    // Set selected IDs
    selectedGenderId.value = student.genderId;
    selectedReligionId.value = student.religionId;
    selectedGradeId.value = student.gradeId;
    selectedClassroomId.value = student.classroomId;
    selectedBusId.value = student.busId;
    selectedTypeBloodId.value = student.typeBloodId;
  }

  /// Add new student
  Future<void> addStudent() async {
    if (!addStudentFormKey.currentState!.validate()) {
      return;
    }

    _isLoading.value = true;

    try {
      // TODO: Implement actual API call to add student
      // For now, simulate API call
      await Future.delayed(const Duration(seconds: 2));

      // Show success message
      Get.snackbar(
        'Success',
        'Student added successfully',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green,
        colorText: Colors.white,
        margin: const EdgeInsets.all(16),
        borderRadius: 12,
      );

      // Clear form and go back
      clearFormControllers();
      Get.back();

      // Refresh students list
      await refreshStudents();
    } catch (e) {
      LoggerService.error('Error adding student', error: e);
      Get.snackbar(
        'Error',
        'Failed to add student: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
        margin: const EdgeInsets.all(16),
        borderRadius: 12,
      );
    } finally {
      _isLoading.value = false;
    }
  }

  /// Update existing student
  Future<void> updateStudent(String? studentId) async {
    if (!editStudentFormKey.currentState!.validate()) {
      return;
    }

    if (studentId == null) {
      Get.snackbar(
        'Error',
        'Student ID is required for update',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
        margin: const EdgeInsets.all(16),
        borderRadius: 12,
      );
      return;
    }

    _isLoading.value = true;

    try {
      // TODO: Implement actual API call to update student
      // For now, simulate API call
      await Future.delayed(const Duration(seconds: 2));

      // Show success message
      Get.snackbar(
        'Success',
        'Student updated successfully',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green,
        colorText: Colors.white,
        margin: const EdgeInsets.all(16),
        borderRadius: 12,
      );

      // Clear form and go back
      clearFormControllers();
      Get.back();

      // Refresh students list
      await refreshStudents();
    } catch (e) {
      LoggerService.error('Error updating student', error: e);
      Get.snackbar(
        'Error',
        'Failed to update student: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
        margin: const EdgeInsets.all(16),
        borderRadius: 12,
      );
    } finally {
      _isLoading.value = false;
    }
  }

  @override
  void onClose() {
    // Dispose controllers
    nameController.dispose();
    phoneController.dispose();
    addressController.dispose();
    cityNameController.dispose();
    dateBirthController.dispose();
    genderController.dispose();
    religionController.dispose();
    gradeController.dispose();
    classroomController.dispose();
    busController.dispose();
    typeBloodController.dispose();
    tripTypeController.dispose();
    super.onClose();
  }

  /// Generate and download Excel template
  Future<void> downloadTemplate() async {
    try {
      final templateBytes = ExcelService.generateTemplate();

      // For web, we'll trigger download
      // TODO: Implement actual file download for web
      Get.snackbar(
        'Template Generated',
        'Excel template has been generated successfully',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green.withValues(alpha: 0.9),
        colorText: Colors.white,
        margin: const EdgeInsets.all(16),
        borderRadius: 12,
      );
    } catch (e) {
      LoggerService.error('Error generating template', error: e);
      Get.snackbar(
        'Error',
        'Failed to generate template: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
        margin: const EdgeInsets.all(16),
        borderRadius: 12,
      );
    }
  }

  /// Pick and import Excel file
  Future<void> importFromExcel() async {
    try {
      _isImporting.value = true;
      _importProgress.value = 'Selecting file...';

      // Pick Excel file
      final result = await ExcelService.pickExcelFile();
      if (result == null) {
        _isImporting.value = false;
        return;
      }

      final file = result.files.first;
      _importProgress.value = 'Parsing Excel file...';

      // Parse Excel file
      final parseResult = await ExcelService.parseExcelFile(file.bytes!);

      if (parseResult.hasErrors) {
        _showImportErrors(parseResult);
        return;
      }

      if (parseResult.students.isEmpty) {
        Get.snackbar(
          'No Data',
          'No valid student data found in the Excel file',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.orange,
          colorText: Colors.white,
          margin: const EdgeInsets.all(16),
          borderRadius: 12,
        );
        return;
      }

      // Import students
      await _importStudents(parseResult.students);

    } catch (e) {
      LoggerService.error('Error importing Excel file', error: e);
      Get.snackbar(
        'Import Error',
        'Failed to import Excel file: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
        margin: const EdgeInsets.all(16),
        borderRadius: 12,
      );
    } finally {
      _isImporting.value = false;
      _importProgress.value = '';
    }
  }

  /// Import students from parsed data
  Future<void> _importStudents(List<StudentImportData> studentsData) async {
    _importedCount.value = 0;
    _importErrorCount.value = 0;

    final totalStudents = studentsData.length;

    for (int i = 0; i < studentsData.length; i++) {
      final studentData = studentsData[i];
      _importProgress.value = 'Importing student ${i + 1} of $totalStudents...';

      try {
        // TODO: Implement actual API call to create student
        // For now, simulate API call
        await Future.delayed(const Duration(milliseconds: 500));

        final student = studentData.toStudent();
        // Add to local list for immediate UI update
        _students.add(student);
        _importedCount.value++;

      } catch (e) {
        LoggerService.error('Error importing student at row ${studentData.rowNumber}', error: e);
        _importErrorCount.value++;
      }
    }

    // Show import results
    _showImportResults();

    // Refresh the students list
    await refreshStudents();
  }

  /// Show import errors dialog
  void _showImportErrors(ExcelParseResult parseResult) {
    Get.dialog(
      Dialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        child: Container(
          padding: const EdgeInsets.all(24),
          constraints: const BoxConstraints(maxWidth: 600, maxHeight: 500),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Row(
                children: [
                  Icon(Icons.error_outline, color: Colors.red, size: 24),
                  const SizedBox(width: 12),
                  Text(
                    'Import Errors Found',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.red,
                    ),
                  ),
                  const Spacer(),
                  IconButton(
                    onPressed: () => Get.back(),
                    icon: const Icon(Icons.close),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Text(
                'Found ${parseResult.errorCount} errors in ${parseResult.totalRows} rows:',
                style: const TextStyle(fontSize: 14),
              ),
              const SizedBox(height: 16),
              Expanded(
                child: ListView.builder(
                  itemCount: parseResult.errors.length,
                  itemBuilder: (context, index) {
                    final error = parseResult.errors[index];
                    return Card(
                      margin: const EdgeInsets.only(bottom: 8),
                      child: Padding(
                        padding: const EdgeInsets.all(12),
                        child: Row(
                          children: [
                            Container(
                              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                              decoration: BoxDecoration(
                                color: Colors.red.withValues(alpha: 0.1),
                                borderRadius: BorderRadius.circular(4),
                              ),
                              child: Text(
                                'Row ${error.row}',
                                style: TextStyle(
                                  fontSize: 12,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.red,
                                ),
                              ),
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              child: Text(
                                error.message,
                                style: const TextStyle(fontSize: 13),
                              ),
                            ),
                          ],
                        ),
                      ),
                    );
                  },
                ),
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: TextButton(
                      onPressed: () => Get.back(),
                      child: const Text('Close'),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () {
                        Get.back();
                        downloadTemplate();
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.blue,
                        foregroundColor: Colors.white,
                      ),
                      child: const Text('Download Template'),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Show import results
  void _showImportResults() {
    final successCount = _importedCount.value;
    final errorCount = _importErrorCount.value;
    final totalCount = successCount + errorCount;

    Get.dialog(
      Dialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        child: Container(
          padding: const EdgeInsets.all(24),
          constraints: const BoxConstraints(maxWidth: 400),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                successCount > 0 ? Icons.check_circle : Icons.error,
                color: successCount > 0 ? Colors.green : Colors.red,
                size: 48,
              ),
              const SizedBox(height: 16),
              Text(
                'Import Complete',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: successCount > 0 ? Colors.green : Colors.red,
                ),
              ),
              const SizedBox(height: 16),
              Text(
                'Total processed: $totalCount students',
                style: const TextStyle(fontSize: 14),
              ),
              const SizedBox(height: 8),
              Text(
                'Successfully imported: $successCount',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.green,
                  fontWeight: FontWeight.w500,
                ),
              ),
              if (errorCount > 0) ...[
                const SizedBox(height: 4),
                Text(
                  'Failed to import: $errorCount',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.red,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
              const SizedBox(height: 24),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: () => Get.back(),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: successCount > 0 ? Colors.green : Colors.red,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                  ),
                  child: const Text('Close'),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
