import 'package:get/get.dart';
import '../../domain/entities/supervisor.dart';
import '../../domain/usecases/get_supervisors_usecase.dart';
import '../../domain/usecases/get_supervisor_by_id_usecase.dart';
import '../../domain/usecases/create_supervisor_usecase.dart';
import '../../domain/usecases/update_supervisor_usecase.dart';
import '../../domain/usecases/delete_supervisor_usecase.dart';
import '../../domain/repositories/supervisor_repository.dart';
import '../../core/utils/logger.dart';

/// Supervisors controller using GetX state management
/// Following Single Responsibility Principle by focusing only on supervisors state management
class SupervisorsController extends GetxController {
  final GetSupervisorsUseCase _getSupervisorsUseCase;
  final GetSupervisorByIdUseCase _getSupervisorByIdUseCase;
  final CreateSupervisorUseCase _createSupervisorUseCase;
  final UpdateSupervisorUseCase _updateSupervisorUseCase;
  final DeleteSupervisorUseCase _deleteSupervisorUseCase;
  final SupervisorRepository _supervisorRepository;

  SupervisorsController({
    required GetSupervisorsUseCase getSupervisorsUseCase,
    required GetSupervisorByIdUseCase getSupervisorByIdUseCase,
    required CreateSupervisorUseCase createSupervisorUseCase,
    required UpdateSupervisorUseCase updateSupervisorUseCase,
    required DeleteSupervisorUseCase deleteSupervisorUseCase,
    required SupervisorRepository supervisorRepository,
  })  : _getSupervisorsUseCase = getSupervisorsUseCase,
        _getSupervisorByIdUseCase = getSupervisorByIdUseCase,
        _createSupervisorUseCase = createSupervisorUseCase,
        _updateSupervisorUseCase = updateSupervisorUseCase,
        _deleteSupervisorUseCase = deleteSupervisorUseCase,
        _supervisorRepository = supervisorRepository;

  // Observable state
  final _supervisors = <Supervisor>[].obs;
  final _isLoading = false.obs;
  final _isLoadingMore = false.obs;
  final _errorMessage = ''.obs;
  final _currentPage = 1.obs;
  final _hasMoreData = true.obs;
  final _searchQuery = ''.obs;

  // Form state
  final _availableBuses = <Map<String, dynamic>>[].obs;
  final _genderOptions = <Map<String, dynamic>>[].obs;
  final _religionOptions = <Map<String, dynamic>>[].obs;
  final _bloodTypeOptions = <Map<String, dynamic>>[].obs;
  final _isLoadingFormData = false.obs;

  // Getters
  List<Supervisor> get supervisors => _supervisors;
  bool get isLoading => _isLoading.value;
  bool get isLoadingMore => _isLoadingMore.value;
  String get errorMessage => _errorMessage.value;
  int get currentPage => _currentPage.value;
  bool get hasMoreData => _hasMoreData.value;
  String get searchQuery => _searchQuery.value;

  List<Map<String, dynamic>> get availableBuses => _availableBuses;
  List<Map<String, dynamic>> get genderOptions => _genderOptions;
  List<Map<String, dynamic>> get religionOptions => _religionOptions;
  List<Map<String, dynamic>> get bloodTypeOptions => _bloodTypeOptions;
  bool get isLoadingFormData => _isLoadingFormData.value;

  @override
  void onInit() {
    super.onInit();
    loadSupervisors();
    loadFormData();
  }

  /// Load supervisors with pagination
  Future<void> loadSupervisors({bool refresh = false}) async {
    try {
      if (refresh) {
        _currentPage.value = 1;
        _hasMoreData.value = true;
        _supervisors.clear();
      }

      if (!_hasMoreData.value && !refresh) return;

      if (_currentPage.value == 1) {
        _isLoading.value = true;
      } else {
        _isLoadingMore.value = true;
      }

      _errorMessage.value = '';

      LoggerService.debug('Loading supervisors', data: {
        'page': _currentPage.value,
        'refresh': refresh,
      });

      final result = await _getSupervisorsUseCase(GetSupervisorsParams(
        page: _currentPage.value,
        limit: 10,
      ));

      result.fold(
        (failure) {
          LoggerService.error('Failed to load supervisors', error: failure);
          _errorMessage.value = failure.message;
          Get.snackbar(
            'خطأ',
            failure.message,
            snackPosition: SnackPosition.BOTTOM,
          );
        },
        (newSupervisors) {
          LoggerService.info('Successfully loaded ${newSupervisors.length} supervisors');
          
          if (refresh) {
            _supervisors.assignAll(newSupervisors);
          } else {
            _supervisors.addAll(newSupervisors);
          }

          // Check if there's more data
          if (newSupervisors.length < 10) {
            _hasMoreData.value = false;
          } else {
            _currentPage.value++;
          }
        },
      );
    } catch (e) {
      LoggerService.error('Unexpected error loading supervisors', error: e);
      _errorMessage.value = 'حدث خطأ غير متوقع';
      Get.snackbar(
        'خطأ',
        'حدث خطأ غير متوقع',
        snackPosition: SnackPosition.BOTTOM,
      );
    } finally {
      _isLoading.value = false;
      _isLoadingMore.value = false;
    }
  }

  /// Load more supervisors for pagination
  Future<void> loadMoreSupervisors() async {
    if (!_isLoadingMore.value && _hasMoreData.value) {
      await loadSupervisors();
    }
  }

  /// Search supervisors
  void searchSupervisors(String query) {
    _searchQuery.value = query;
    // Implement search logic here if needed
    // For now, we'll just refresh the list
    loadSupervisors(refresh: true);
  }

  /// Get supervisor by ID
  Future<Supervisor?> getSupervisorById(int id) async {
    try {
      LoggerService.debug('Getting supervisor by ID', data: {'supervisorId': id});

      final result = await _getSupervisorByIdUseCase(id);
      
      return result.fold(
        (failure) {
          LoggerService.error('Failed to get supervisor', error: failure);
          Get.snackbar(
            'خطأ',
            failure.message,
            snackPosition: SnackPosition.BOTTOM,
          );
          return null;
        },
        (supervisor) {
          LoggerService.info('Successfully retrieved supervisor', data: {'supervisorId': id});
          return supervisor;
        },
      );
    } catch (e) {
      LoggerService.error('Unexpected error getting supervisor', error: e);
      Get.snackbar(
        'خطأ',
        'حدث خطأ غير متوقع',
        snackPosition: SnackPosition.BOTTOM,
      );
      return null;
    }
  }

  /// Create new supervisor
  Future<bool> createSupervisor(Supervisor supervisor) async {
    try {
      LoggerService.debug('Creating supervisor', data: {'supervisorName': supervisor.name});

      _isLoading.value = true;
      _errorMessage.value = '';

      final result = await _createSupervisorUseCase(supervisor);

      return result.fold(
        (failure) {
          LoggerService.error('Failed to create supervisor', error: failure);
          _errorMessage.value = failure.message;
          Get.snackbar(
            'خطأ',
            failure.message,
            snackPosition: SnackPosition.BOTTOM,
          );
          return false;
        },
        (createdSupervisor) {
          LoggerService.info('Successfully created supervisor', data: {'supervisorId': createdSupervisor.id});
          
          // Add to the beginning of the list
          _supervisors.insert(0, createdSupervisor);
          
          Get.snackbar(
            'نجح',
            'تم إنشاء المشرف بنجاح',
            snackPosition: SnackPosition.BOTTOM,
          );
          return true;
        },
      );
    } catch (e) {
      LoggerService.error('Unexpected error creating supervisor', error: e);
      _errorMessage.value = 'حدث خطأ غير متوقع';
      Get.snackbar(
        'خطأ',
        'حدث خطأ غير متوقع',
        snackPosition: SnackPosition.BOTTOM,
      );
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// Update existing supervisor
  Future<bool> updateSupervisor(Supervisor supervisor) async {
    try {
      LoggerService.debug('Updating supervisor', data: {'supervisorId': supervisor.id});

      _isLoading.value = true;
      _errorMessage.value = '';

      final result = await _updateSupervisorUseCase(supervisor);

      return result.fold(
        (failure) {
          LoggerService.error('Failed to update supervisor', error: failure);
          _errorMessage.value = failure.message;
          Get.snackbar(
            'خطأ',
            failure.message,
            snackPosition: SnackPosition.BOTTOM,
          );
          return false;
        },
        (updatedSupervisor) {
          LoggerService.info('Successfully updated supervisor', data: {'supervisorId': supervisor.id});

          // Update in the list
          final index = _supervisors.indexWhere((s) => s.id == supervisor.id);
          if (index != -1) {
            _supervisors[index] = updatedSupervisor;
          }

          Get.snackbar(
            'نجح',
            'تم تحديث المشرف بنجاح',
            snackPosition: SnackPosition.BOTTOM,
          );
          return true;
        },
      );
    } catch (e) {
      LoggerService.error('Unexpected error updating supervisor', error: e);
      _errorMessage.value = 'حدث خطأ غير متوقع';
      Get.snackbar(
        'خطأ',
        'حدث خطأ غير متوقع',
        snackPosition: SnackPosition.BOTTOM,
      );
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// Delete supervisor
  Future<bool> deleteSupervisor(int supervisorId) async {
    try {
      LoggerService.debug('Deleting supervisor', data: {'supervisorId': supervisorId});

      _isLoading.value = true;
      _errorMessage.value = '';

      final result = await _deleteSupervisorUseCase(supervisorId);

      return result.fold(
        (failure) {
          LoggerService.error('Failed to delete supervisor', error: failure);
          _errorMessage.value = failure.message;
          Get.snackbar(
            'خطأ',
            failure.message,
            snackPosition: SnackPosition.BOTTOM,
          );
          return false;
        },
        (success) {
          LoggerService.info('Successfully deleted supervisor', data: {'supervisorId': supervisorId});

          // Remove from the list
          _supervisors.removeWhere((s) => s.id == supervisorId);

          Get.snackbar(
            'نجح',
            'تم حذف المشرف بنجاح',
            snackPosition: SnackPosition.BOTTOM,
          );
          return true;
        },
      );
    } catch (e) {
      LoggerService.error('Unexpected error deleting supervisor', error: e);
      _errorMessage.value = 'حدث خطأ غير متوقع';
      Get.snackbar(
        'خطأ',
        'حدث خطأ غير متوقع',
        snackPosition: SnackPosition.BOTTOM,
      );
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// Load form data (buses, genders, religions, blood types)
  Future<void> loadFormData() async {
    try {
      _isLoadingFormData.value = true;
      LoggerService.debug('Loading form data for supervisors');

      // Load all form data concurrently
      final results = await Future.wait([
        _supervisorRepository.getAvailableBuses(),
        _supervisorRepository.getGenderOptions(),
        _supervisorRepository.getReligionOptions(),
        _supervisorRepository.getBloodTypeOptions(),
      ]);

      // Process available buses
      results[0].fold(
        (failure) => LoggerService.error('Failed to load available buses', error: failure),
        (buses) => _availableBuses.assignAll(buses),
      );

      // Process gender options
      results[1].fold(
        (failure) => LoggerService.error('Failed to load gender options', error: failure),
        (genders) => _genderOptions.assignAll(genders),
      );

      // Process religion options
      results[2].fold(
        (failure) => LoggerService.error('Failed to load religion options', error: failure),
        (religions) => _religionOptions.assignAll(religions),
      );

      // Process blood type options
      results[3].fold(
        (failure) => LoggerService.error('Failed to load blood type options', error: failure),
        (bloodTypes) => _bloodTypeOptions.assignAll(bloodTypes),
      );

      LoggerService.info('Successfully loaded form data', data: {
        'busesCount': _availableBuses.length,
        'gendersCount': _genderOptions.length,
        'religionsCount': _religionOptions.length,
        'bloodTypesCount': _bloodTypeOptions.length,
      });
    } catch (e) {
      LoggerService.error('Unexpected error loading form data', error: e);
    } finally {
      _isLoadingFormData.value = false;
    }
  }

  /// Refresh all data
  Future<void> refreshData() async {
    await Future.wait([
      loadSupervisors(refresh: true),
      loadFormData(),
    ]);
  }

  /// Clear error message
  void clearError() {
    _errorMessage.value = '';
  }
}
