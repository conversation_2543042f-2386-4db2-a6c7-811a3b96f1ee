import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/constants/color_constants.dart';
import '../../../core/utils/responsive_utils.dart';
import '../../../core/widgets/responsive_builder.dart';
import '../../controllers/auth_controller.dart';
import '../../routes/app_routes.dart';
import '../../widgets/auth/auth_header.dart';
import '../../widgets/auth/auth_text_field.dart';
import '../../widgets/common/custom_button.dart';

/// ForgotPasswordPage for password recovery
/// Following Single Responsibility Principle by focusing only on password recovery UI
class ForgotPasswordPage extends StatefulWidget {
  const ForgotPasswordPage({Key? key}) : super(key: key);

  @override
  State<ForgotPasswordPage> createState() => _ForgotPasswordPageState();
}

class _ForgotPasswordPageState extends State<ForgotPasswordPage> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _authController = Get.find<AuthController>();

  @override
  void dispose() {
    _emailController.dispose();
    super.dispose();
  }

  /// Validate form and send password reset email
  Future<void> _forgotPassword() async {
    if (_formKey.currentState!.validate()) {
      final success = await _authController.forgotPassword(
        _emailController.text.trim(),
      );

      if (success) {
        Get.snackbar(
          'تم إرسال رابط إعادة تعيين كلمة المرور',
          'تم إرسال رابط إعادة تعيين كلمة المرور إلى بريدك الإلكتروني',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: ColorConstants.greenSuccess.withAlpha(150),
          colorText: ColorConstants.white,
          margin: const EdgeInsets.all(16),
        );
        Get.toNamed(AppRoutes.resetPassword);
      } else {
        Get.snackbar(
          'خطأ',
          _authController.errorMessage,
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: ColorConstants.error.withAlpha(150),
          colorText: ColorConstants.white,
          margin: const EdgeInsets.all(16),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'نسيت كلمة المرور',
          style: TextStyle(
            color: ColorConstants.white,
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true,
        backgroundColor: ColorConstants.primary,
        elevation: 0,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: ColorConstants.white,
          ),
          onPressed: () => Get.back(),
        ),
      ),
      body: ResponsiveBuilder(
        builder: (context, screenType) {
          return SafeArea(
            child: Center(
              child: SingleChildScrollView(
                child: Container(
                  width: ResponsiveUtils.getResponsiveValue<double>(
                    context: context,
                    mobile: MediaQuery.of(context).size.width * 0.9,
                    tablet: 500,
                    desktop: 600,
                  ),
                  padding: const EdgeInsets.all(24),
                  margin: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: ColorConstants.white,
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                      BoxShadow(
                        color: ColorConstants.black.withAlpha(20),
                        blurRadius: 10,
                        offset: const Offset(0, 5),
                      ),
                    ],
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // Header with logo and title
                      const AuthHeader(
                        title: 'نسيت كلمة المرور',
                        subtitle: 'أدخل بريدك الإلكتروني لإعادة تعيين كلمة المرور',
                      ),
                      const SizedBox(height: 32),
                      
                      // Forgot password form
                      Form(
                        key: _formKey,
                        child: Column(
                          children: [
                            // Email field
                            AuthTextField(
                              controller: _emailController,
                              label: 'البريد الإلكتروني',
                              hint: 'أدخل البريد الإلكتروني',
                              icon: Icons.email_outlined,
                              keyboardType: TextInputType.emailAddress,
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return 'الرجاء إدخال البريد الإلكتروني';
                                }
                                if (!GetUtils.isEmail(value)) {
                                  return 'الرجاء إدخال بريد إلكتروني صحيح';
                                }
                                return null;
                              },
                            ),
                            const SizedBox(height: 24),
                            
                            // Submit button
                            Obx(() {
                              return CustomButton(
                                text: 'إرسال',
                                isLoading: _authController.isLoading,
                                onPressed: _forgotPassword,
                              );
                            }),
                            const SizedBox(height: 24),
                            
                            // Login link
                            Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Text(
                                  'تذكرت كلمة المرور؟',
                                  style: TextStyle(
                                    color: ColorConstants.textForm,
                                    fontSize: 14,
                                  ),
                                ),
                                TextButton(
                                  onPressed: () {
                                    Get.toNamed(AppRoutes.login);
                                  },
                                  child: Text(
                                    'تسجيل الدخول',
                                    style: TextStyle(
                                      color: ColorConstants.primary,
                                      fontSize: 14,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}
