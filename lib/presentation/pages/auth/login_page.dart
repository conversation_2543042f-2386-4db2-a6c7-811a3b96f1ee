import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/constants/color_constants.dart';
import '../../../core/utils/logger.dart';
import '../../../core/utils/responsive_utils.dart';
import '../../../core/widgets/responsive_builder.dart';
import '../../controllers/auth_controller.dart';
import '../../routes/app_routes.dart';
import '../../widgets/auth/auth_header.dart';
import '../../widgets/auth/auth_text_field.dart';
import '../../widgets/common/custom_button.dart';

/// LoginPage for user authentication
/// Following Single Responsibility Principle by focusing only on login UI
class LoginPage extends StatefulWidget {
  const LoginPage({super.key});

  @override
  State<LoginPage> createState() => _LoginPageState();
}

class _LoginPageState extends State<LoginPage> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _authController = Get.find<AuthController>();
  bool _obscurePassword = true;
  bool _rememberMe = false;

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  /// Validate form and login
  Future<void> _login() async {
    LoggerService.info('Login button pressed');

    if (_formKey.currentState!.validate()) {
      LoggerService.debug('Form validation passed, attempting login');

      try {
        final email = _emailController.text.trim();
        final password = _passwordController.text;

        LoggerService.debug(
          'Calling auth controller login method',
          data: {'email': email, 'rememberMe': _rememberMe},
        );

        final success = await _authController.login(email, password);

        if (success) {
          LoggerService.success('Login successful, navigating to home');
          Get.offAllNamed(AppRoutes.home);
        } else {
          LoggerService.error(
            'Login failed',
            error: _authController.errorMessage,
          );
          Get.snackbar(
            'خطأ في تسجيل الدخول',
            _authController.errorMessage,
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: ColorConstants.error.withAlpha(150),
            colorText: ColorConstants.white,
            margin: const EdgeInsets.all(16),
          );
        }
      } catch (e) {
        LoggerService.error('Unexpected error during login', error: e);
        Get.snackbar(
          'خطأ غير متوقع',
          'حدث خطأ غير متوقع أثناء تسجيل الدخول. يرجى المحاولة مرة أخرى.',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: ColorConstants.error.withAlpha(150),
          colorText: ColorConstants.white,
          margin: const EdgeInsets.all(16),
        );
      }
    } else {
      LoggerService.warning('Form validation failed');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: ResponsiveBuilder(
        builder: (context, screenType) {
          return SafeArea(
            child: Center(
              child: SingleChildScrollView(
                child: Container(
                  width: ResponsiveUtils.getResponsiveValue<double>(
                    context: context,
                    mobile: MediaQuery.of(context).size.width * 0.9,
                    tablet: 500,
                    desktop: 600,
                  ),
                  padding: const EdgeInsets.all(24),
                  decoration: BoxDecoration(
                    color: ColorConstants.white,
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                      BoxShadow(
                        color: ColorConstants.black.withAlpha(20),
                        blurRadius: 10,
                        offset: const Offset(0, 5),
                      ),
                    ],
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // Header with logo and title
                      const AuthHeader(
                        title: 'تسجيل الدخول',
                        subtitle: 'أدخل بيانات الدخول للوصول إلى حسابك',
                      ),
                      const SizedBox(height: 32),

                      // Login form
                      Form(
                        key: _formKey,
                        child: Column(
                          children: [
                            // Email field
                            AuthTextField(
                              controller: _emailController,
                              label: 'البريد الإلكتروني',
                              hint: 'أدخل البريد الإلكتروني',
                              icon: Icons.email_outlined,
                              keyboardType: TextInputType.emailAddress,
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return 'الرجاء إدخال البريد الإلكتروني';
                                }
                                if (!GetUtils.isEmail(value)) {
                                  return 'الرجاء إدخال بريد إلكتروني صحيح';
                                }
                                return null;
                              },
                            ),
                            const SizedBox(height: 16),

                            // Password field
                            AuthTextField(
                              controller: _passwordController,
                              label: 'كلمة المرور',
                              hint: 'أدخل كلمة المرور',
                              icon: Icons.lock_outline,
                              obscureText: _obscurePassword,
                              suffixIcon: IconButton(
                                icon: Icon(
                                  _obscurePassword
                                      ? Icons.visibility_outlined
                                      : Icons.visibility_off_outlined,
                                  color: ColorConstants.iconInputColor,
                                ),
                                onPressed: () {
                                  setState(() {
                                    _obscurePassword = !_obscurePassword;
                                  });
                                },
                              ),
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return 'الرجاء إدخال كلمة المرور';
                                }
                                if (value.length < 6) {
                                  return 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
                                }
                                return null;
                              },
                            ),
                            const SizedBox(height: 16),

                            // Remember me and forgot password
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                // Remember me
                                Row(
                                  children: [
                                    Checkbox(
                                      value: _rememberMe,
                                      activeColor: ColorConstants.primary,
                                      onChanged: (value) {
                                        setState(() {
                                          _rememberMe = value ?? false;
                                        });
                                      },
                                    ),
                                    Text(
                                      'تذكرني',
                                      style: TextStyle(
                                        color: ColorConstants.textForm,
                                        fontSize: 14,
                                      ),
                                    ),
                                  ],
                                ),

                                // Forgot password
                                TextButton(
                                  onPressed: () {
                                    Get.toNamed(AppRoutes.forgotPassword);
                                  },
                                  child: Text(
                                    'نسيت كلمة المرور؟',
                                    style: TextStyle(
                                      color: ColorConstants.primary,
                                      fontSize: 14,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 24),

                            // Login button
                            Obx(() {
                              return CustomButton(
                                text: 'تسجيل الدخول',
                                isLoading: _authController.isLoading,
                                onPressed: _login,
                              );
                            }),
                            const SizedBox(height: 24),

                            // Register link
                            Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Text(
                                  'ليس لديك حساب؟',
                                  style: TextStyle(
                                    color: ColorConstants.textForm,
                                    fontSize: 14,
                                  ),
                                ),
                                TextButton(
                                  onPressed: () {
                                    Get.toNamed(AppRoutes.register);
                                  },
                                  child: Text(
                                    'إنشاء حساب',
                                    style: TextStyle(
                                      color: ColorConstants.primary,
                                      fontSize: 14,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ),
                              ],
                            ),

                            // Test login button (only in debug mode)
                            const SizedBox(height: 16),
                            OutlinedButton(
                              onPressed: () async {
                                LoggerService.info('Test login button pressed');

                                // Set test credentials
                                _emailController.text = '<EMAIL>';
                                _passwordController.text = 'password';

                                // Attempt login with test credentials
                                await _login();
                              },
                              style: OutlinedButton.styleFrom(
                                side: BorderSide(color: ColorConstants.primary),
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 16,
                                  vertical: 8,
                                ),
                              ),
                              child: Text(
                                'تسجيل دخول تجريبي',
                                style: TextStyle(
                                  color: ColorConstants.primary,
                                  fontSize: 14,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}
