import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/constants/color_constants.dart';
import '../../../core/utils/responsive_utils.dart';
import '../../../core/widgets/responsive_builder.dart';
import '../../../data/models/user_model.dart';
import '../../controllers/auth_controller.dart';
import '../../routes/app_routes.dart';
import '../../widgets/auth/auth_header.dart';
import '../../widgets/auth/auth_text_field.dart';
import '../../widgets/common/custom_button.dart';

/// RegisterPage for user registration
/// Following Single Responsibility Principle by focusing only on registration UI
class RegisterPage extends StatefulWidget {
  const RegisterPage({Key? key}) : super(key: key);

  @override
  State<RegisterPage> createState() => _RegisterPageState();
}

class _RegisterPageState extends State<RegisterPage> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _emailController = TextEditingController();
  final _phoneController = TextEditingController();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  final _authController = Get.find<AuthController>();
  bool _obscurePassword = true;
  bool _obscureConfirmPassword = true;
  String _selectedUserType = 'parent'; // Default user type

  final List<Map<String, dynamic>> _userTypes = [
    {'value': 'parent', 'label': 'ولي أمر'},
    {'value': 'school', 'label': 'مدرسة'},
    {'value': 'driver', 'label': 'سائق'},
    {'value': 'supervisor', 'label': 'مشرف'},
  ];

  @override
  void dispose() {
    _nameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  /// Validate form and register
  Future<void> _register() async {
    if (_formKey.currentState!.validate()) {
      // Create user model
      final user = UserModel(
        id: '', // Will be generated by the server
        name: _nameController.text.trim(),
        email: _emailController.text.trim(),
        phone: _phoneController.text.trim(),
        type: _selectedUserType,
      );

      final success = await _authController.register(
        user,
        _passwordController.text,
      );

      if (success) {
        Get.offAllNamed(AppRoutes.home);
      } else {
        Get.snackbar(
          'خطأ في التسجيل',
          _authController.errorMessage,
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: ColorConstants.error.withAlpha(150),
          colorText: ColorConstants.white,
          margin: const EdgeInsets.all(16),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: ResponsiveBuilder(
        builder: (context, screenType) {
          return SafeArea(
            child: Center(
              child: SingleChildScrollView(
                child: Container(
                  width: ResponsiveUtils.getResponsiveValue<double>(
                    context: context,
                    mobile: MediaQuery.of(context).size.width * 0.9,
                    tablet: 600,
                    desktop: 700,
                  ),
                  padding: const EdgeInsets.all(24),
                  margin: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: ColorConstants.white,
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                      BoxShadow(
                        color: ColorConstants.black.withAlpha(20),
                        blurRadius: 10,
                        offset: const Offset(0, 5),
                      ),
                    ],
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // Header with logo and title
                      const AuthHeader(
                        title: 'إنشاء حساب جديد',
                        subtitle: 'أدخل بياناتك لإنشاء حساب جديد',
                      ),
                      const SizedBox(height: 32),
                      
                      // Registration form
                      Form(
                        key: _formKey,
                        child: Column(
                          children: [
                            // Name field
                            AuthTextField(
                              controller: _nameController,
                              label: 'الاسم',
                              hint: 'أدخل الاسم الكامل',
                              icon: Icons.person_outline,
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return 'الرجاء إدخال الاسم';
                                }
                                return null;
                              },
                            ),
                            const SizedBox(height: 16),
                            
                            // Email field
                            AuthTextField(
                              controller: _emailController,
                              label: 'البريد الإلكتروني',
                              hint: 'أدخل البريد الإلكتروني',
                              icon: Icons.email_outlined,
                              keyboardType: TextInputType.emailAddress,
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return 'الرجاء إدخال البريد الإلكتروني';
                                }
                                if (!GetUtils.isEmail(value)) {
                                  return 'الرجاء إدخال بريد إلكتروني صحيح';
                                }
                                return null;
                              },
                            ),
                            const SizedBox(height: 16),
                            
                            // Phone field
                            AuthTextField(
                              controller: _phoneController,
                              label: 'رقم الهاتف',
                              hint: 'أدخل رقم الهاتف',
                              icon: Icons.phone_outlined,
                              keyboardType: TextInputType.phone,
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return 'الرجاء إدخال رقم الهاتف';
                                }
                                return null;
                              },
                            ),
                            const SizedBox(height: 16),
                            
                            // User type dropdown
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'نوع المستخدم',
                                  style: TextStyle(
                                    fontSize: 14,
                                    fontWeight: FontWeight.bold,
                                    color: ColorConstants.text,
                                  ),
                                ),
                                const SizedBox(height: 8),
                                Container(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 16,
                                    vertical: 4,
                                  ),
                                  decoration: BoxDecoration(
                                    color: ColorConstants.fillFormField,
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  child: DropdownButtonHideUnderline(
                                    child: DropdownButton<String>(
                                      value: _selectedUserType,
                                      isExpanded: true,
                                      icon: Icon(
                                        Icons.arrow_drop_down,
                                        color: ColorConstants.iconInputColor,
                                      ),
                                      style: TextStyle(
                                        fontSize: 14,
                                        color: ColorConstants.text,
                                      ),
                                      onChanged: (String? newValue) {
                                        if (newValue != null) {
                                          setState(() {
                                            _selectedUserType = newValue;
                                          });
                                        }
                                      },
                                      items: _userTypes.map((type) {
                                        return DropdownMenuItem<String>(
                                          value: type['value'],
                                          child: Text(type['label']),
                                        );
                                      }).toList(),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 16),
                            
                            // Password field
                            AuthTextField(
                              controller: _passwordController,
                              label: 'كلمة المرور',
                              hint: 'أدخل كلمة المرور',
                              icon: Icons.lock_outline,
                              obscureText: _obscurePassword,
                              suffixIcon: IconButton(
                                icon: Icon(
                                  _obscurePassword
                                      ? Icons.visibility_outlined
                                      : Icons.visibility_off_outlined,
                                  color: ColorConstants.iconInputColor,
                                ),
                                onPressed: () {
                                  setState(() {
                                    _obscurePassword = !_obscurePassword;
                                  });
                                },
                              ),
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return 'الرجاء إدخال كلمة المرور';
                                }
                                if (value.length < 6) {
                                  return 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
                                }
                                return null;
                              },
                            ),
                            const SizedBox(height: 16),
                            
                            // Confirm password field
                            AuthTextField(
                              controller: _confirmPasswordController,
                              label: 'تأكيد كلمة المرور',
                              hint: 'أدخل تأكيد كلمة المرور',
                              icon: Icons.lock_outline,
                              obscureText: _obscureConfirmPassword,
                              suffixIcon: IconButton(
                                icon: Icon(
                                  _obscureConfirmPassword
                                      ? Icons.visibility_outlined
                                      : Icons.visibility_off_outlined,
                                  color: ColorConstants.iconInputColor,
                                ),
                                onPressed: () {
                                  setState(() {
                                    _obscureConfirmPassword = !_obscureConfirmPassword;
                                  });
                                },
                              ),
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return 'الرجاء إدخال تأكيد كلمة المرور';
                                }
                                if (value != _passwordController.text) {
                                  return 'كلمة المرور غير متطابقة';
                                }
                                return null;
                              },
                            ),
                            const SizedBox(height: 24),
                            
                            // Register button
                            Obx(() {
                              return CustomButton(
                                text: 'إنشاء حساب',
                                isLoading: _authController.isLoading,
                                onPressed: _register,
                              );
                            }),
                            const SizedBox(height: 24),
                            
                            // Login link
                            Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Text(
                                  'لديك حساب بالفعل؟',
                                  style: TextStyle(
                                    color: ColorConstants.textForm,
                                    fontSize: 14,
                                  ),
                                ),
                                TextButton(
                                  onPressed: () {
                                    Get.toNamed(AppRoutes.login);
                                  },
                                  child: Text(
                                    'تسجيل الدخول',
                                    style: TextStyle(
                                      color: ColorConstants.primary,
                                      fontSize: 14,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}
