import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/constants/color_constants.dart';
import '../../../core/utils/responsive_utils.dart';
import '../../../core/widgets/responsive_builder.dart';
import '../../controllers/auth_controller.dart';
import '../../routes/app_routes.dart';
import '../../widgets/auth/auth_header.dart';
import '../../widgets/auth/auth_text_field.dart';
import '../../widgets/common/custom_button.dart';

/// ResetPasswordPage for resetting password
/// Following Single Responsibility Principle by focusing only on password reset UI
class ResetPasswordPage extends StatefulWidget {
  const ResetPasswordPage({Key? key}) : super(key: key);

  @override
  State<ResetPasswordPage> createState() => _ResetPasswordPageState();
}

class _ResetPasswordPageState extends State<ResetPasswordPage> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _codeController = TextEditingController();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  final _authController = Get.find<AuthController>();
  bool _obscurePassword = true;
  bool _obscureConfirmPassword = true;

  @override
  void dispose() {
    _emailController.dispose();
    _codeController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  /// Validate form and reset password
  Future<void> _resetPassword() async {
    if (_formKey.currentState!.validate()) {
      final success = await _authController.resetPassword(
        _emailController.text.trim(),
        _codeController.text.trim(),
        _passwordController.text,
      );

      if (success) {
        Get.snackbar(
          'تم إعادة تعيين كلمة المرور',
          'تم إعادة تعيين كلمة المرور بنجاح',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: ColorConstants.greenSuccess.withAlpha(150),
          colorText: ColorConstants.white,
          margin: const EdgeInsets.all(16),
        );
        Get.offAllNamed(AppRoutes.login);
      } else {
        Get.snackbar(
          'خطأ',
          _authController.errorMessage,
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: ColorConstants.error.withAlpha(150),
          colorText: ColorConstants.white,
          margin: const EdgeInsets.all(16),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'إعادة تعيين كلمة المرور',
          style: TextStyle(
            color: ColorConstants.white,
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true,
        backgroundColor: ColorConstants.primary,
        elevation: 0,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: ColorConstants.white,
          ),
          onPressed: () => Get.back(),
        ),
      ),
      body: ResponsiveBuilder(
        builder: (context, screenType) {
          return SafeArea(
            child: Center(
              child: SingleChildScrollView(
                child: Container(
                  width: ResponsiveUtils.getResponsiveValue<double>(
                    context: context,
                    mobile: MediaQuery.of(context).size.width * 0.9,
                    tablet: 500,
                    desktop: 600,
                  ),
                  padding: const EdgeInsets.all(24),
                  margin: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: ColorConstants.white,
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                      BoxShadow(
                        color: ColorConstants.black.withAlpha(20),
                        blurRadius: 10,
                        offset: const Offset(0, 5),
                      ),
                    ],
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // Header with logo and title
                      const AuthHeader(
                        title: 'إعادة تعيين كلمة المرور',
                        subtitle: 'أدخل البيانات المطلوبة لإعادة تعيين كلمة المرور',
                      ),
                      const SizedBox(height: 32),
                      
                      // Reset password form
                      Form(
                        key: _formKey,
                        child: Column(
                          children: [
                            // Email field
                            AuthTextField(
                              controller: _emailController,
                              label: 'البريد الإلكتروني',
                              hint: 'أدخل البريد الإلكتروني',
                              icon: Icons.email_outlined,
                              keyboardType: TextInputType.emailAddress,
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return 'الرجاء إدخال البريد الإلكتروني';
                                }
                                if (!GetUtils.isEmail(value)) {
                                  return 'الرجاء إدخال بريد إلكتروني صحيح';
                                }
                                return null;
                              },
                            ),
                            const SizedBox(height: 16),
                            
                            // Code field
                            AuthTextField(
                              controller: _codeController,
                              label: 'رمز التحقق',
                              hint: 'أدخل رمز التحقق المرسل إلى بريدك الإلكتروني',
                              icon: Icons.security_outlined,
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return 'الرجاء إدخال رمز التحقق';
                                }
                                return null;
                              },
                            ),
                            const SizedBox(height: 16),
                            
                            // Password field
                            AuthTextField(
                              controller: _passwordController,
                              label: 'كلمة المرور الجديدة',
                              hint: 'أدخل كلمة المرور الجديدة',
                              icon: Icons.lock_outline,
                              obscureText: _obscurePassword,
                              suffixIcon: IconButton(
                                icon: Icon(
                                  _obscurePassword
                                      ? Icons.visibility_outlined
                                      : Icons.visibility_off_outlined,
                                  color: ColorConstants.iconInputColor,
                                ),
                                onPressed: () {
                                  setState(() {
                                    _obscurePassword = !_obscurePassword;
                                  });
                                },
                              ),
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return 'الرجاء إدخال كلمة المرور الجديدة';
                                }
                                if (value.length < 6) {
                                  return 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
                                }
                                return null;
                              },
                            ),
                            const SizedBox(height: 16),
                            
                            // Confirm password field
                            AuthTextField(
                              controller: _confirmPasswordController,
                              label: 'تأكيد كلمة المرور الجديدة',
                              hint: 'أدخل تأكيد كلمة المرور الجديدة',
                              icon: Icons.lock_outline,
                              obscureText: _obscureConfirmPassword,
                              suffixIcon: IconButton(
                                icon: Icon(
                                  _obscureConfirmPassword
                                      ? Icons.visibility_outlined
                                      : Icons.visibility_off_outlined,
                                  color: ColorConstants.iconInputColor,
                                ),
                                onPressed: () {
                                  setState(() {
                                    _obscureConfirmPassword = !_obscureConfirmPassword;
                                  });
                                },
                              ),
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return 'الرجاء إدخال تأكيد كلمة المرور الجديدة';
                                }
                                if (value != _passwordController.text) {
                                  return 'كلمة المرور غير متطابقة';
                                }
                                return null;
                              },
                            ),
                            const SizedBox(height: 24),
                            
                            // Submit button
                            Obx(() {
                              return CustomButton(
                                text: 'إعادة تعيين كلمة المرور',
                                isLoading: _authController.isLoading,
                                onPressed: _resetPassword,
                              );
                            }),
                            const SizedBox(height: 24),
                            
                            // Login link
                            Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Text(
                                  'تذكرت كلمة المرور؟',
                                  style: TextStyle(
                                    color: ColorConstants.textForm,
                                    fontSize: 14,
                                  ),
                                ),
                                TextButton(
                                  onPressed: () {
                                    Get.toNamed(AppRoutes.login);
                                  },
                                  child: Text(
                                    'تسجيل الدخول',
                                    style: TextStyle(
                                      color: ColorConstants.primary,
                                      fontSize: 14,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}
