import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../core/constants/color_constants.dart';
import '../../../core/utils/responsive_utils.dart';
import '../../controllers/buses_controller.dart';
import '../../widgets/buses/bus_form.dart';
import '../../widgets/layout/responsive_sidebar.dart';

/// Add bus page
/// Following Single Responsibility Principle by focusing only on add bus UI
class AddBusPage extends StatelessWidget {
  const AddBusPage({super.key});

  @override
  Widget build(BuildContext context) {
    // Get the buses controller
    final controller = Get.find<BusesController>();

    return Scaffold(
      backgroundColor: ColorConstants.background,
      body: ResponsiveSidebar(
        child: _buildAddBusContent(context, controller),
      ),
    );
  }

  /// Build add bus content
  Widget _buildAddBusContent(BuildContext context, BusesController controller) {
    final isDesktop = ResponsiveUtils.isDesktop(context);

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          _buildHeader(context, isDesktop),
          const SizedBox(height: 24),

          // Form
          BusForm(
            controller: controller,
            formKey: controller.addBusFormKey,
            isEdit: false,
            onSubmit: () => controller.addBus(),
          ),
        ],
      ),
    );
  }

  /// Build header
  Widget _buildHeader(BuildContext context, bool isDesktop) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // Back button
          IconButton(
            onPressed: () => Get.back(),
            icon: Icon(
              Icons.arrow_back_rounded,
              color: ColorConstants.textSecondary,
            ),
            style: IconButton.styleFrom(
              backgroundColor: Colors.grey.shade100,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
          const SizedBox(width: 16),

          // Icon and title
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: ColorConstants.primary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(
              Icons.add_rounded,
              color: ColorConstants.primary,
              size: isDesktop ? 28 : 24,
            ),
          ),
          const SizedBox(width: 16),

          // Title and subtitle
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Add New Bus',
                  style: TextStyle(
                    fontSize: isDesktop ? 28 : 24,
                    fontWeight: FontWeight.bold,
                    color: ColorConstants.textPrimary,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'Add a new bus to the school transportation system',
                  style: TextStyle(
                    fontSize: isDesktop ? 16 : 14,
                    color: ColorConstants.textSecondary,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
