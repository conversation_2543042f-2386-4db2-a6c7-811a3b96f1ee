import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:responsive_framework/responsive_framework.dart'
    hide ResponsiveUtils;
import '../../../core/constants/color_constants.dart';
import '../../../core/utils/responsive_utils.dart';
import '../../controllers/dashboard_controller.dart';
import '../../widgets/dashboard/dashboard_header.dart';
import '../../widgets/dashboard/dashboard_stats_card.dart';
import '../../widgets/dashboard/recent_trips_card.dart';
import '../../widgets/dashboard/school_stats_chart.dart';
import '../../widgets/dashboard/upcoming_trips_card.dart';
import '../../widgets/layout/responsive_sidebar.dart';

/// Dashboard page
/// Following Single Responsibility Principle by focusing only on dashboard UI
class DashboardPage extends StatelessWidget {
  const DashboardPage({super.key});

  @override
  Widget build(BuildContext context) {
    // Get the dashboard controller
    final controller = Get.find<DashboardController>();

    return Scaffold(
      backgroundColor: ColorConstants.background,
      body: ResponsiveSidebar(
        child: _buildDashboardContent(context, controller),
      ),
    );
  }

  /// Build dashboard content
  Widget _buildDashboardContent(
    BuildContext context,
    DashboardController controller,
  ) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Dashboard header
          const DashboardHeader(),
          const SizedBox(height: 24),

          // Dashboard stats cards
          _buildStatsCards(context, controller),
          const SizedBox(height: 24),

          // Dashboard charts and tables
          _buildChartsAndTables(context, controller),
        ],
      ),
    );
  }

  /// Build stats cards
  Widget _buildStatsCards(
    BuildContext context,
    DashboardController controller,
  ) {
    return ResponsiveRowColumn(
      layout:
          ResponsiveUtils.getDeviceType(context) == DeviceScreenType.mobile
              ? ResponsiveRowColumnType.COLUMN
              : ResponsiveRowColumnType.ROW,
      rowCrossAxisAlignment: CrossAxisAlignment.start,
      rowSpacing: 16,
      columnSpacing: 16,
      children: [
        // Total schools card
        ResponsiveRowColumnItem(
          rowFlex: 1,
          child: DashboardStatsCard(
            title: 'المدارس',
            value: controller.totalSchools.toString(),
            icon: Icons.school_rounded,
            iconColor: ColorConstants.primary,
            iconBackgroundColor: ColorConstants.primary.withAlpha(26),
            onTap: controller.navigateToSchools,
          ),
        ),

        // Total students card
        ResponsiveRowColumnItem(
          rowFlex: 1,
          child: DashboardStatsCard(
            title: 'الطلاب',
            value: controller.totalStudents.toString(),
            icon: Icons.people_alt_rounded,
            iconColor: ColorConstants.secondary,
            iconBackgroundColor: ColorConstants.secondary.withAlpha(26),
            onTap: controller.navigateToStudents,
          ),
        ),

        // Total buses card
        ResponsiveRowColumnItem(
          rowFlex: 1,
          child: DashboardStatsCard(
            title: 'الحافلات',
            value: controller.totalBuses.toString(),
            icon: Icons.directions_bus_rounded,
            iconColor: ColorConstants.accent1,
            iconBackgroundColor: ColorConstants.accent1.withAlpha(26),
            onTap: controller.navigateToBuses,
          ),
        ),

        // Total trips card
        ResponsiveRowColumnItem(
          rowFlex: 1,
          child: DashboardStatsCard(
            title: 'الرحلات',
            value: controller.totalTrips.toString(),
            icon: Icons.map_rounded,
            iconColor: ColorConstants.accent2,
            iconBackgroundColor: ColorConstants.accent2.withAlpha(26),
            onTap: controller.navigateToTrips,
          ),
        ),
      ],
    );
  }

  /// Build charts and tables
  Widget _buildChartsAndTables(
    BuildContext context,
    DashboardController controller,
  ) {
    final isDesktop =
        ResponsiveUtils.getDeviceType(context) == DeviceScreenType.desktop;
    final isTablet =
        ResponsiveUtils.getDeviceType(context) == DeviceScreenType.tablet;

    if (isDesktop) {
      // Desktop layout (3 columns)
      return Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // School stats chart (50% width)
          Expanded(
            flex: 5,
            child: SchoolStatsChart(schoolStats: controller.schoolStats),
          ),
          const SizedBox(width: 16),

          // Right column (50% width)
          Expanded(
            flex: 5,
            child: Column(
              children: [
                // Upcoming trips
                UpcomingTripsCard(
                  upcomingTrips: controller.upcomingTrips,
                  onViewAll: controller.navigateToTrips,
                ),
                const SizedBox(height: 16),

                // Recent trips
                RecentTripsCard(
                  recentTrips: controller.recentTrips,
                  onViewAll: controller.navigateToTrips,
                ),
              ],
            ),
          ),
        ],
      );
    } else if (isTablet) {
      // Tablet layout (2 columns)
      return Column(
        children: [
          // First row
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // School stats chart (50% width)
              Expanded(
                child: SchoolStatsChart(schoolStats: controller.schoolStats),
              ),
              const SizedBox(width: 16),

              // Upcoming trips (50% width)
              Expanded(
                child: UpcomingTripsCard(
                  upcomingTrips: controller.upcomingTrips,
                  onViewAll: controller.navigateToTrips,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Recent trips (full width)
          RecentTripsCard(
            recentTrips: controller.recentTrips,
            onViewAll: controller.navigateToTrips,
          ),
        ],
      );
    } else {
      // Mobile layout (1 column)
      return Column(
        children: [
          // School stats chart
          SchoolStatsChart(schoolStats: controller.schoolStats),
          const SizedBox(height: 16),

          // Upcoming trips
          UpcomingTripsCard(
            upcomingTrips: controller.upcomingTrips,
            onViewAll: controller.navigateToTrips,
          ),
          const SizedBox(height: 16),

          // Recent trips
          RecentTripsCard(
            recentTrips: controller.recentTrips,
            onViewAll: controller.navigateToTrips,
          ),
        ],
      );
    }
  }
}
