import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../core/network/api_service.dart';
import '../../../core/utils/logger.dart';
import '../../../data/models/bus_model.dart';
import '../../../data/repositories/bus_repository_impl.dart';

/// Debug page for testing Bus API integration
class BusApiDebugPage extends StatefulWidget {
  const BusApiDebugPage({super.key});

  @override
  State<BusApiDebugPage> createState() => _BusApiDebugPageState();
}

class _BusApiDebugPageState extends State<BusApiDebugPage> {
  final _repository = BusRepositoryImpl(Get.find<ApiService>());
  final _scrollController = ScrollController();
  final List<String> _logs = [];
  bool _isLoading = false;

  void _addLog(String message) {
    setState(() {
      _logs.add('${DateTime.now().toIso8601String()}: $message');
    });
    // Auto scroll to bottom
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  Future<void> _testGetBuses() async {
    setState(() => _isLoading = true);
    _addLog('🔄 Testing GET buses API...');

    try {
      final result = await _repository.getBuses(page: 1, perPage: 5);
      
      result.fold(
        (failure) {
          _addLog('❌ GET buses failed: ${failure.message}');
        },
        (buses) {
          _addLog('✅ GET buses success: Found ${buses.length} buses');
          for (final bus in buses) {
            _addLog('   📍 Bus: ${bus.name} (${bus.carNumber})');
          }
        },
      );
    } catch (e) {
      _addLog('💥 GET buses error: $e');
    }

    setState(() => _isLoading = false);
  }

  Future<void> _testCreateBus() async {
    setState(() => _isLoading = true);
    _addLog('🔄 Testing CREATE bus API...');

    try {
      final testBus = BusModel(
        name: 'Test Bus ${DateTime.now().millisecondsSinceEpoch}',
        carNumber: 'TEST-${DateTime.now().millisecondsSinceEpoch % 1000}',
        notes: 'Created from debug page',
      );

      final result = await _repository.createBus(testBus);
      
      result.fold(
        (failure) {
          _addLog('❌ CREATE bus failed: ${failure.message}');
        },
        (bus) {
          _addLog('✅ CREATE bus success: ${bus.name} (ID: ${bus.id})');
        },
      );
    } catch (e) {
      _addLog('💥 CREATE bus error: $e');
    }

    setState(() => _isLoading = false);
  }

  Future<void> _testApiConnection() async {
    setState(() => _isLoading = true);
    _addLog('🔄 Testing API connection...');

    try {
      final apiService = Get.find<ApiService>();
      final token = await apiService.getToken();
      
      if (token != null && token.isNotEmpty) {
        _addLog('✅ Auth token found: ${token.substring(0, 20)}...');
      } else {
        _addLog('⚠️ No auth token found');
      }

      // Test basic API call
      final response = await apiService.get('buses/all?page=1&limit=1', isAuth: true);
      _addLog('✅ API connection successful: Status ${response.statusCode}');
      _addLog('📊 Response data: ${response.data.toString().substring(0, 100)}...');
      
    } catch (e) {
      _addLog('💥 API connection error: $e');
    }

    setState(() => _isLoading = false);
  }

  void _clearLogs() {
    setState(() {
      _logs.clear();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Bus API Debug'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: Column(
        children: [
          // Control buttons
          Container(
            padding: const EdgeInsets.all(16),
            child: Wrap(
              spacing: 8,
              runSpacing: 8,
              children: [
                ElevatedButton.icon(
                  onPressed: _isLoading ? null : _testApiConnection,
                  icon: const Icon(Icons.wifi),
                  label: const Text('Test Connection'),
                ),
                ElevatedButton.icon(
                  onPressed: _isLoading ? null : _testGetBuses,
                  icon: const Icon(Icons.list),
                  label: const Text('Test Get Buses'),
                ),
                ElevatedButton.icon(
                  onPressed: _isLoading ? null : _testCreateBus,
                  icon: const Icon(Icons.add),
                  label: const Text('Test Create Bus'),
                ),
                ElevatedButton.icon(
                  onPressed: _clearLogs,
                  icon: const Icon(Icons.clear),
                  label: const Text('Clear Logs'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red,
                    foregroundColor: Colors.white,
                  ),
                ),
              ],
            ),
          ),
          
          // Loading indicator
          if (_isLoading)
            const LinearProgressIndicator(),
          
          // Logs display
          Expanded(
            child: Container(
              margin: const EdgeInsets.all(16),
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.black87,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey),
              ),
              child: _logs.isEmpty
                  ? const Center(
                      child: Text(
                        'No logs yet. Click a test button to start.',
                        style: TextStyle(color: Colors.grey),
                      ),
                    )
                  : ListView.builder(
                      controller: _scrollController,
                      itemCount: _logs.length,
                      itemBuilder: (context, index) {
                        final log = _logs[index];
                        Color textColor = Colors.white;
                        
                        if (log.contains('✅')) {
                          textColor = Colors.green;
                        } else if (log.contains('❌') || log.contains('💥')) {
                          textColor = Colors.red;
                        } else if (log.contains('⚠️')) {
                          textColor = Colors.orange;
                        } else if (log.contains('🔄')) {
                          textColor = Colors.blue;
                        }
                        
                        return Padding(
                          padding: const EdgeInsets.symmetric(vertical: 2),
                          child: Text(
                            log,
                            style: TextStyle(
                              color: textColor,
                              fontFamily: 'monospace',
                              fontSize: 12,
                            ),
                          ),
                        );
                      },
                    ),
            ),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }
}
