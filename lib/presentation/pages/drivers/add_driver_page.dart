import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/constants/color_constants.dart';
import '../../../core/utils/responsive_utils.dart';
import '../../../data/models/driver_model.dart';
import '../../controllers/drivers_controller.dart';
import '../../widgets/layout/responsive_sidebar.dart';

/// Add driver page
class AddDriverPage extends StatefulWidget {
  const AddDriverPage({super.key});

  @override
  State<AddDriverPage> createState() => _AddDriverPageState();
}

class _AddDriverPageState extends State<AddDriverPage> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _usernameController = TextEditingController();
  final _phoneController = TextEditingController();
  final _addressController = TextEditingController();
  final _cityController = TextEditingController();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();

  DateTime? _selectedBirthDate;
  DateTime? _selectedJoiningDate;
  int? _selectedGenderId;
  int? _selectedReligionId;

  int? _selectedBusId;

  DriversController? _controller;

  @override
  void initState() {
    super.initState();
    _controller = Get.find<DriversController>();
  }

  @override
  void dispose() {
    _nameController.dispose();
    _usernameController.dispose();
    _phoneController.dispose();
    _addressController.dispose();
    _cityController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ColorConstants.background,
      body: ResponsiveSidebar(
        child: _buildContent(context),
      ),
    );
  }

  Widget _buildContent(BuildContext context) {
    final isDesktop = ResponsiveUtils.isDesktop(context);

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          _buildHeader(context),
          const SizedBox(height: 24),

          // Form
          Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.05),
                  blurRadius: 10,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Form(
              key: _formKey,
              child: isDesktop
                  ? _buildDesktopForm(context)
                  : _buildMobileForm(context),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.white,
            ColorConstants.primary.withValues(alpha: 0.02),
          ],
        ),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: ColorConstants.primary.withValues(alpha: 0.08),
            blurRadius: 20,
            offset: const Offset(0, 8),
          ),
        ],
        border: Border.all(
          color: ColorConstants.primary.withValues(alpha: 0.1),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          // Back button
          IconButton(
            onPressed: () => Get.back(),
            icon: Icon(
              Icons.arrow_back_rounded,
              color: ColorConstants.primary,
            ),
            style: IconButton.styleFrom(
              backgroundColor: ColorConstants.primary.withValues(alpha: 0.1),
              padding: const EdgeInsets.all(12),
            ),
          ),
          const SizedBox(width: 16),

          // Title
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'إضافة سائق جديد',
                  style: TextStyle(
                    fontSize: ResponsiveUtils.getResponsiveFontSizeByDevice(
                      context,
                      mobile: 24,
                      tablet: 28,
                      desktop: 32,
                    ),
                    fontWeight: FontWeight.w800,
                    color: ColorConstants.textPrimary,
                    letterSpacing: -0.5,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'إضافة سائق جديد إلى المدرسة',
                  style: TextStyle(
                    fontSize: ResponsiveUtils.getResponsiveFontSizeByDevice(
                      context,
                      mobile: 14,
                      tablet: 16,
                      desktop: 18,
                    ),
                    color: ColorConstants.textSecondary,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDesktopForm(BuildContext context) {
    return Column(
      children: [
        // Personal Information Section
        _buildSectionTitle('المعلومات الشخصية', Icons.person_rounded),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(child: _buildNameField()),
            const SizedBox(width: 16),
            Expanded(child: _buildUsernameField()),
          ],
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(child: _buildPhoneField()),
            const SizedBox(width: 16),
            Expanded(child: Container()), // Empty space
          ],
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(child: _buildPasswordField()),
            const SizedBox(width: 16),
            Expanded(child: _buildConfirmPasswordField()),
          ],
        ),
        const SizedBox(height: 32),

        // Demographics Section
        _buildSectionTitle('المعلومات الديموغرافية', Icons.info_rounded),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(child: _buildGenderDropdown()),
            const SizedBox(width: 16),
            Expanded(child: _buildReligionDropdown()),
          ],
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(child: _buildBirthDateField()),
            const SizedBox(width: 16),
            Expanded(child: Container()), // Empty space
          ],
        ),
        const SizedBox(height: 32),

        // Employment Section
        _buildSectionTitle('معلومات التوظيف', Icons.work_rounded),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(child: _buildJoiningDateField()),
            const SizedBox(width: 16),
            Expanded(child: _buildBusDropdown()),
          ],
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(child: _buildAddressField()),
            const SizedBox(width: 16),
            Expanded(child: _buildCityField()),
          ],
        ),
        const SizedBox(height: 32),

        // Action buttons
        _buildActionButtons(),
      ],
    );
  }

  Widget _buildMobileForm(BuildContext context) {
    return Column(
      children: [
        // Personal Information Section
        _buildSectionTitle('المعلومات الشخصية', Icons.person_rounded),
        const SizedBox(height: 16),
        _buildNameField(),
        const SizedBox(height: 16),
        _buildUsernameField(),
        const SizedBox(height: 16),
        _buildPhoneField(),
        const SizedBox(height: 16),
        _buildPasswordField(),
        const SizedBox(height: 16),
        _buildConfirmPasswordField(),
        const SizedBox(height: 32),

        // Demographics Section
        _buildSectionTitle('المعلومات الديموغرافية', Icons.info_rounded),
        const SizedBox(height: 16),
        _buildGenderDropdown(),
        const SizedBox(height: 16),
        _buildReligionDropdown(),
        const SizedBox(height: 16),

        _buildBirthDateField(),
        const SizedBox(height: 32),

        // Employment Section
        _buildSectionTitle('معلومات التوظيف', Icons.work_rounded),
        const SizedBox(height: 16),
        _buildJoiningDateField(),
        const SizedBox(height: 16),
        _buildBusDropdown(),
        const SizedBox(height: 16),
        _buildAddressField(),
        const SizedBox(height: 16),
        _buildCityField(),
        const SizedBox(height: 32),

        // Action buttons
        _buildActionButtons(),
      ],
    );
  }

  Widget _buildSectionTitle(String title, IconData icon) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: ColorConstants.primary.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            icon,
            color: ColorConstants.primary,
            size: 20,
          ),
        ),
        const SizedBox(width: 12),
        Text(
          title,
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w700,
            color: ColorConstants.textPrimary,
          ),
        ),
      ],
    );
  }

  Widget _buildNameField() {
    return TextFormField(
      controller: _nameController,
      decoration: InputDecoration(
        labelText: 'الاسم الكامل *',
        hintText: 'أدخل الاسم الكامل',
        prefixIcon: const Icon(Icons.person_outline),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
      validator: (value) {
        if (value == null || value.trim().isEmpty) {
          return 'الاسم مطلوب';
        }
        return null;
      },
    );
  }

  Widget _buildUsernameField() {
    return TextFormField(
      controller: _usernameController,
      decoration: InputDecoration(
        labelText: 'اسم المستخدم *',
        hintText: 'أدخل اسم المستخدم',
        prefixIcon: const Icon(Icons.account_circle_outlined),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
      validator: (value) {
        if (value == null || value.trim().isEmpty) {
          return 'اسم المستخدم مطلوب';
        }
        return null;
      },
    );
  }

  Widget _buildPhoneField() {
    return TextFormField(
      controller: _phoneController,
      keyboardType: TextInputType.phone,
      decoration: InputDecoration(
        labelText: 'رقم الهاتف',
        hintText: 'أدخل رقم الهاتف',
        prefixIcon: const Icon(Icons.phone_outlined),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
    );
  }

  Widget _buildPasswordField() {
    return TextFormField(
      controller: _passwordController,
      obscureText: true,
      decoration: InputDecoration(
        labelText: 'كلمة المرور *',
        hintText: 'أدخل كلمة المرور',
        prefixIcon: const Icon(Icons.lock_outline),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
      validator: (value) {
        if (value == null || value.trim().isEmpty) {
          return 'كلمة المرور مطلوبة';
        }
        if (value.length < 6) {
          return 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
        }
        return null;
      },
    );
  }

  Widget _buildConfirmPasswordField() {
    return TextFormField(
      controller: _confirmPasswordController,
      obscureText: true,
      decoration: InputDecoration(
        labelText: 'تأكيد كلمة المرور *',
        hintText: 'أعد إدخال كلمة المرور',
        prefixIcon: const Icon(Icons.lock_outline),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
      validator: (value) {
        if (value == null || value.trim().isEmpty) {
          return 'تأكيد كلمة المرور مطلوب';
        }
        if (value != _passwordController.text) {
          return 'كلمة المرور غير متطابقة';
        }
        return null;
      },
    );
  }

  Widget _buildGenderDropdown() {
    return Obx(() {
      final genders = _controller?.genderOptions ?? [];

      // Ensure the selected value exists in the list
      final validSelectedGenderId = genders.any((g) => g['id'] == _selectedGenderId)
          ? _selectedGenderId
          : null;

      return DropdownButtonFormField<int>(
        value: validSelectedGenderId,
        decoration: InputDecoration(
          labelText: 'الجنس',
          prefixIcon: const Icon(Icons.person_outline),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
        items: genders.map((gender) {
          return DropdownMenuItem<int>(
            value: gender['id'] as int,
            child: Text(gender['name'] as String),
          );
        }).toList(),
        onChanged: (value) {
          setState(() {
            _selectedGenderId = value;
          });
        },
      );
    });
  }

  Widget _buildReligionDropdown() {
    return Obx(() {
      final religions = _controller?.religionOptions ?? [];

      // Ensure the selected value exists in the list
      final validSelectedReligionId = religions.any((r) => r['id'] == _selectedReligionId)
          ? _selectedReligionId
          : null;

      return DropdownButtonFormField<int>(
        value: validSelectedReligionId,
        decoration: InputDecoration(
          labelText: 'الديانة',
          prefixIcon: const Icon(Icons.account_balance_outlined),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
        items: religions.map((religion) {
          return DropdownMenuItem<int>(
            value: religion['id'] as int,
            child: Text(religion['name'] as String),
          );
        }).toList(),
        onChanged: (value) {
          setState(() {
            _selectedReligionId = value;
          });
        },
      );
    });
  }



  Widget _buildBusDropdown() {
    return Obx(() {
      final buses = _controller?.availableBuses ?? [];
      // Add "no bus" option
      final busOptions = [
        {'id': 0, 'name': 'غير مخصص'},
        ...buses,
      ];

      // Ensure the selected value exists in the list
      final validSelectedBusId = busOptions.any((b) => b['id'] == _selectedBusId)
          ? _selectedBusId
          : 0; // Default to "غير مخصص"

      return DropdownButtonFormField<int>(
        value: validSelectedBusId,
        decoration: InputDecoration(
          labelText: 'الحافلة المخصصة',
          prefixIcon: const Icon(Icons.directions_bus_outlined),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
        items: busOptions.map((bus) {
          return DropdownMenuItem<int>(
            value: bus['id'] as int,
            child: Text(bus['name'] as String),
          );
        }).toList(),
        onChanged: (value) {
          setState(() {
            _selectedBusId = value;
          });
        },
      );
    });
  }

  Widget _buildBirthDateField() {
    return InkWell(
      onTap: () => _selectBirthDate(),
      child: InputDecorator(
        decoration: InputDecoration(
          labelText: 'تاريخ الميلاد',
          prefixIcon: const Icon(Icons.calendar_today_outlined),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
        child: Text(
          _selectedBirthDate != null
              ? '${_selectedBirthDate!.year}-${_selectedBirthDate!.month.toString().padLeft(2, '0')}-${_selectedBirthDate!.day.toString().padLeft(2, '0')}'
              : 'اختر تاريخ الميلاد',
          style: TextStyle(
            color: _selectedBirthDate != null
                ? ColorConstants.textPrimary
                : ColorConstants.textSecondary,
          ),
        ),
      ),
    );
  }

  Widget _buildJoiningDateField() {
    return InkWell(
      onTap: () => _selectJoiningDate(),
      child: InputDecorator(
        decoration: InputDecoration(
          labelText: 'تاريخ الانضمام',
          prefixIcon: const Icon(Icons.work_outline),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
        child: Text(
          _selectedJoiningDate != null
              ? '${_selectedJoiningDate!.year}-${_selectedJoiningDate!.month.toString().padLeft(2, '0')}-${_selectedJoiningDate!.day.toString().padLeft(2, '0')}'
              : 'اختر تاريخ الانضمام',
          style: TextStyle(
            color: _selectedJoiningDate != null
                ? ColorConstants.textPrimary
                : ColorConstants.textSecondary,
          ),
        ),
      ),
    );
  }

  Widget _buildAddressField() {
    return TextFormField(
      controller: _addressController,
      maxLines: 2,
      decoration: InputDecoration(
        labelText: 'العنوان',
        hintText: 'أدخل العنوان',
        prefixIcon: const Icon(Icons.location_on_outlined),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
    );
  }

  Widget _buildCityField() {
    return TextFormField(
      controller: _cityController,
      decoration: InputDecoration(
        labelText: 'المدينة',
        hintText: 'أدخل المدينة',
        prefixIcon: const Icon(Icons.location_city_outlined),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
    );
  }

  Widget _buildActionButtons() {
    return Obx(() {
      final isLoading = _controller?.isLoading ?? false;
      
      return Row(
        children: [
          Expanded(
            child: ElevatedButton(
              onPressed: isLoading ? null : _saveDriver,
              style: ElevatedButton.styleFrom(
                backgroundColor: ColorConstants.primary,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: isLoading
                  ? const SizedBox(
                      height: 20,
                      width: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : const Text(
                      'إضافة السائق',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: OutlinedButton(
              onPressed: isLoading ? null : () => Get.back(),
              style: OutlinedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: const Text(
                'إلغاء',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
        ],
      );
    });
  }

  Future<void> _selectBirthDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: DateTime.now().subtract(const Duration(days: 365 * 25)),
      firstDate: DateTime(1950),
      lastDate: DateTime.now(),
    );
    if (date != null) {
      setState(() {
        _selectedBirthDate = date;
      });
    }
  }

  Future<void> _selectJoiningDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(2000),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );
    if (date != null) {
      setState(() {
        _selectedJoiningDate = date;
      });
    }
  }

  Future<void> _saveDriver() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (_controller == null) {
      return;
    }

    // Create new driver model
    final newDriver = DriverModel(
      name: _nameController.text.trim(),
      username: _usernameController.text.trim(),
      phone: _phoneController.text.trim().isNotEmpty ? _phoneController.text.trim() : null,
      address: _addressController.text.trim().isNotEmpty ? _addressController.text.trim() : null,
      cityName: _cityController.text.trim().isNotEmpty ? _cityController.text.trim() : null,
      genderId: _selectedGenderId,
      religionId: _selectedReligionId,
      typeBloodId: null, // فصيلة الدم غير مستخدمة
      busId: _selectedBusId,
      birthDate: _selectedBirthDate?.toIso8601String().split('T')[0],
      joiningDate: _selectedJoiningDate?.toIso8601String().split('T')[0],
    );

    final success = await _controller!.createDriver(
      newDriver,
      password: _passwordController.text.trim(),
      passwordConfirmation: _confirmPasswordController.text.trim(),
    );

    if (success) {
      Get.back(); // Go back to drivers list
    }
  }
}
