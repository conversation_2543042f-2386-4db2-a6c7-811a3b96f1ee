import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/constants/color_constants.dart';
import '../../../core/utils/responsive_utils.dart';
import '../../../domain/entities/driver.dart';
import '../../controllers/drivers_controller.dart';
import '../../widgets/layout/responsive_sidebar.dart';
import '../../routes/app_routes.dart';

/// Driver details page
class DriverDetailsPage extends StatelessWidget {
  const DriverDetailsPage({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<DriversController>();
    final Driver? driver = Get.arguments as Driver?;

    // If no driver passed as argument, try to get from controller
    final displayDriver = driver ?? controller.selectedDriver;

    if (displayDriver == null) {
      return Scaffold(
        backgroundColor: ColorConstants.background,
        body: ResponsiveSidebar(
          child: _buildErrorState(),
        ),
      );
    }

    return Scaffold(
      backgroundColor: ColorConstants.background,
      body: ResponsiveSidebar(
        child: _buildContent(context, displayDriver, controller),
      ),
    );
  }

  Widget _buildContent(BuildContext context, Driver driver, DriversController controller) {
    final isDesktop = ResponsiveUtils.isDesktop(context);
    final isTablet = ResponsiveUtils.isTablet(context);

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with back button and actions
          _buildHeader(context, driver, isDesktop),
          const SizedBox(height: 24),

          // Driver information cards
          if (isDesktop || isTablet)
            _buildDesktopLayout(context, driver)
          else
            _buildMobileLayout(context, driver),
        ],
      ),
    );
  }

  /// Build header with back button and actions
  Widget _buildHeader(BuildContext context, Driver driver, bool isDesktop) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.white,
            ColorConstants.primary.withValues(alpha: 0.02),
          ],
        ),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: ColorConstants.primary.withValues(alpha: 0.08),
            blurRadius: 20,
            offset: const Offset(0, 8),
          ),
        ],
        border: Border.all(
          color: ColorConstants.primary.withValues(alpha: 0.1),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Back button and title row
          Row(
            children: [
              // Back button
              IconButton(
                onPressed: () => Get.back(),
                icon: Icon(
                  Icons.arrow_back_rounded,
                  color: ColorConstants.primary,
                ),
                style: IconButton.styleFrom(
                  backgroundColor: ColorConstants.primary.withValues(alpha: 0.1),
                  padding: const EdgeInsets.all(12),
                ),
              ),
              const SizedBox(width: 16),

              // Driver avatar and name
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      ColorConstants.primary,
                      ColorConstants.primary.withValues(alpha: 0.8),
                    ],
                  ),
                  borderRadius: BorderRadius.circular(30),
                  boxShadow: [
                    BoxShadow(
                      color: ColorConstants.primary.withValues(alpha: 0.3),
                      blurRadius: 8,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: Center(
                  child: Text(
                    driver.name?.isNotEmpty == true
                        ? driver.name![0].toUpperCase()
                        : 'S',
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 24,
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 16),

              // Driver name and title
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      driver.name ?? 'غير محدد',
                      style: TextStyle(
                        fontSize: ResponsiveUtils.getResponsiveFontSizeByDevice(
                          context,
                          mobile: 24,
                          tablet: 28,
                          desktop: 32,
                        ),
                        fontWeight: FontWeight.w800,
                        color: ColorConstants.textPrimary,
                        letterSpacing: -0.5,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'سائق المدرسة',
                      style: TextStyle(
                        fontSize: ResponsiveUtils.getResponsiveFontSizeByDevice(
                          context,
                          mobile: 14,
                          tablet: 16,
                          desktop: 18,
                        ),
                        color: ColorConstants.textSecondary,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),

              // Action buttons
              if (isDesktop) ...[
                const SizedBox(width: 16),
                _buildActionButtons(driver),
              ],
            ],
          ),

          // Mobile action buttons
          if (!isDesktop) ...[
            const SizedBox(height: 16),
            _buildActionButtons(driver),
          ],
        ],
      ),
    );
  }

  /// Build action buttons
  Widget _buildActionButtons(Driver driver) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Edit button
        ElevatedButton.icon(
          onPressed: () => Get.toNamed(AppRoutes.editDriver, arguments: driver),
          icon: const Icon(Icons.edit_rounded),
          label: const Text('تعديل'),
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.orange,
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
          ),
        ),
        const SizedBox(width: 12),

        // Delete button
        ElevatedButton.icon(
          onPressed: () => _showDeleteConfirmation(driver),
          icon: const Icon(Icons.delete_rounded),
          label: const Text('حذف'),
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.red,
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
          ),
        ),
      ],
    );
  }

  /// Build desktop layout
  Widget _buildDesktopLayout(BuildContext context, Driver driver) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Left column
        Expanded(
          child: Column(
            children: [
              _buildPersonalInfoCard(context, driver),
              const SizedBox(height: 24),
              _buildEmploymentCard(context, driver),
            ],
          ),
        ),
        const SizedBox(width: 24),

        // Right column
        Expanded(
          child: Column(
            children: [
              _buildDemographicsCard(context, driver),
              const SizedBox(height: 24),
              _buildBusAssignmentCard(context, driver),
            ],
          ),
        ),
      ],
    );
  }

  /// Build mobile layout
  Widget _buildMobileLayout(BuildContext context, Driver driver) {
    return Column(
      children: [
        _buildPersonalInfoCard(context, driver),
        const SizedBox(height: 24),
        _buildDemographicsCard(context, driver),
        const SizedBox(height: 24),
        _buildEmploymentCard(context, driver),
        const SizedBox(height: 24),
        _buildBusAssignmentCard(context, driver),
      ],
    );
  }

  /// Build personal information card
  Widget _buildPersonalInfoCard(BuildContext context, Driver driver) {
    return _buildInfoCard(
      context: context,
      title: 'المعلومات الشخصية',
      icon: Icons.person_rounded,
      children: [
        _buildInfoRow('الاسم الكامل', driver.name ?? 'غير محدد'),
        _buildInfoRow('اسم المستخدم', driver.username ?? 'غير محدد'),
        _buildInfoRow('رقم الهاتف', driver.phone ?? 'غير محدد'),
        _buildInfoRow('البريد الإلكتروني', driver.email ?? 'غير محدد'),
      ],
    );
  }

  /// Build demographics card
  Widget _buildDemographicsCard(BuildContext context, Driver driver) {
    return _buildInfoCard(
      context: context,
      title: 'المعلومات الديموغرافية',
      icon: Icons.info_rounded,
      children: [
        _buildInfoRow('الجنس', driver.genderName ?? 'غير محدد'),
        _buildInfoRow('الديانة', driver.religionName ?? 'غير محدد'),
        _buildInfoRow('تاريخ الميلاد', driver.birthDate ?? 'غير محدد'),
      ],
    );
  }

  /// Build employment card
  Widget _buildEmploymentCard(BuildContext context, Driver driver) {
    return _buildInfoCard(
      context: context,
      title: 'معلومات التوظيف',
      icon: Icons.work_rounded,
      children: [
        _buildInfoRow('تاريخ الانضمام', driver.joiningDate ?? 'غير محدد'),
        _buildInfoRow('العنوان', driver.address ?? 'غير محدد'),
        _buildInfoRow('المدينة', driver.cityName ?? 'غير محدد'),
        _buildInfoRow('الحالة', driver.status == 1 ? 'نشط' : 'غير نشط'),
      ],
    );
  }

  /// Build bus assignment card
  Widget _buildBusAssignmentCard(BuildContext context, Driver driver) {
    return _buildInfoCard(
      context: context,
      title: 'تخصيص الحافلة',
      icon: Icons.directions_bus_rounded,
      children: [
        _buildInfoRow('اسم الحافلة', driver.busName ?? 'غير مخصص'),
        _buildInfoRow('رقم الحافلة', driver.busCarNumber ?? 'غير محدد'),
        if (driver.busId != null && driver.busId != 0)
          _buildInfoRow('معرف الحافلة', driver.busId.toString()),
      ],
    );
  }

  /// Build info card wrapper
  Widget _buildInfoCard({
    required BuildContext context,
    required String title,
    required IconData icon,
    required List<Widget> children,
  }) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
        border: Border.all(
          color: Colors.grey.shade100,
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Card header
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: ColorConstants.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  icon,
                  color: ColorConstants.primary,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Text(
                title,
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w700,
                  color: ColorConstants.textPrimary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),

          // Card content
          ...children,
        ],
      ),
    );
  }

  /// Build info row
  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            flex: 2,
            child: Text(
              label,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: ColorConstants.textSecondary,
              ),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            flex: 3,
            child: Text(
              value,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: ColorConstants.textPrimary,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Build error state
  Widget _buildErrorState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline_rounded,
            size: 64,
            color: Colors.red.shade300,
          ),
          const SizedBox(height: 16),
          Text(
            'لم يتم العثور على بيانات السائق',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: ColorConstants.textPrimary,
            ),
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () => Get.back(),
            icon: const Icon(Icons.arrow_back_rounded),
            label: const Text('العودة'),
            style: ElevatedButton.styleFrom(
              backgroundColor: ColorConstants.primary,
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  /// Show delete confirmation dialog
  void _showDeleteConfirmation(Driver driver) {
    Get.dialog(
      AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('هل أنت متأكد من حذف السائق "${driver.name}"؟'),
            const SizedBox(height: 8),
            Text(
              'لا يمكن التراجع عن هذا الإجراء.',
              style: TextStyle(
                color: Colors.red.shade600,
                fontSize: 12,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              Get.back(); // Close dialog
              final controller = Get.find<DriversController>();
              if (driver.id != null) {
                final success = await controller.deleteDriver(driver.id!);
                if (success) {
                  Get.back(); // Go back to drivers list
                }
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }
}
