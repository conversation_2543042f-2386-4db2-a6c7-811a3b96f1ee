import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/constants/color_constants.dart';
import '../../../core/utils/responsive_utils.dart';
import '../../controllers/drivers_controller.dart';
import '../../widgets/drivers/drivers_header.dart';
import '../../widgets/drivers/drivers_table.dart';
import '../../widgets/layout/responsive_sidebar.dart';

/// Drivers page
class DriversPage extends StatelessWidget {
  const DriversPage({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<DriversController>();

    return Scaffold(
      backgroundColor: ColorConstants.background,
      body: ResponsiveSidebar(
        child: _buildContent(context, controller),
      ),
    );
  }

  Widget _buildContent(BuildContext context, DriversController controller) {
    // Calculate available screen height
    final screenHeight = MediaQuery.of(context).size.height;
    final isDesktop = ResponsiveUtils.isDesktop(context);

    // Determine table height based on screen size
    // On desktop, make the table take more space
    final tableHeight = isDesktop
        ? screenHeight - 180 // Larger height for desktop
        : screenHeight - 220; // Smaller height for mobile

    return RefreshIndicator(
      onRefresh: () async {
        await controller.refreshDrivers();
      },
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Drivers header with title, search, and add button
            const DriversHeader(),
            const SizedBox(height: 24),

            // Drivers table with pagination
            Container(
              height: tableHeight,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.05),
                    blurRadius: 10,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(16),
                child: DriversTable(
                  height: tableHeight,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
