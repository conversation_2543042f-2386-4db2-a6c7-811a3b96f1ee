import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../core/constants/color_constants.dart';
import '../../controllers/home_controller.dart';
import '../../widgets/home/<USER>';
import '../../widgets/home/<USER>';
import '../../widgets/home/<USER>';
import '../../widgets/home/<USER>';
import '../../widgets/home/<USER>';
import '../../widgets/layout/responsive_sidebar.dart';

/// Home page
/// Following Single Responsibility Principle by focusing only on home UI
class HomePage extends StatelessWidget {
  const HomePage({super.key});

  @override
  Widget build(BuildContext context) {
    // Get the home controller
    final controller = Get.find<HomeController>();

    return Scaffold(
      backgroundColor: Theme.of(context).brightness == Brightness.dark
          ? ColorConstants.backgroundDark
          : ColorConstants.background,
      body: ResponsiveSidebar(child: _buildHomeContent(context, controller)),
    );
  }

  /// Build home content
  Widget _buildHomeContent(BuildContext context, HomeController controller) {
    return RefreshIndicator(
      onRefresh: () async {
        // Refresh home data
        await controller.refreshHomeData();
      },
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Home header with greeting and profile info
            const HomeHeader(),
            const SizedBox(height: 24),

            // Home address
            const HomeAddress(),
            const SizedBox(height: 24),

            // Home grid with main navigation options
            HomeGrid(controller: controller),
            const SizedBox(height: 24),

            // Current trip card if available
            Obx(
              () =>
                  controller.hasCurrentTrip
                      ? CurrentTripCard(
                        trip: controller.currentTrip!,
                        onTap: controller.navigateToCurrentTrip,
                      )
                      : const SizedBox(),
            ),

            // Ads carousel if available
            Obx(
              () =>
                  controller.hasAds
                      ? Padding(
                        padding: const EdgeInsets.only(top: 24),
                        child: CarouselSliderWidget(ads: controller.ads),
                      )
                      : const SizedBox(),
            ),

            // Bottom padding
            const SizedBox(height: 24),
          ],
        ),
      ),
    );
  }
}
