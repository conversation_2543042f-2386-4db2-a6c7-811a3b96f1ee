import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/constants/color_constants.dart';
import '../../../core/localization/language_controller.dart';
import '../../../core/utils/asset_helper.dart';
import '../../../core/utils/controller_utils.dart';
import '../../routes/app_routes.dart';

/// LanguageSelectionPage for selecting language on first app launch
class LanguageSelectionPage extends StatelessWidget {
  const LanguageSelectionPage({super.key});

  @override
  Widget build(BuildContext context) {
    // Get the language controller safely using our utility function
    final LanguageController languageController =
        ControllerUtils.getLanguageController();

    return Scaffold(
      body: SafeArea(
        child: Center(
          child: Padding(
            padding: const EdgeInsets.all(24.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Logo
                AssetHelper.logoImage,
                const SizedBox(height: 48),

                // Welcome text
                const Text(
                  'Welcome to Busaty',
                  style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 16),

                const Text(
                  'Please select your preferred language',
                  style: TextStyle(fontSize: 16, color: Colors.black54),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 48),

                // Language options
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    _buildLanguageOption(
                      languageController,
                      'English',
                      AssetHelper.englishFlag,
                      'en',
                    ),
                    const SizedBox(width: 24),
                    _buildLanguageOption(
                      languageController,
                      'العربية',
                      AssetHelper.arabicFlag,
                      'ar',
                    ),
                  ],
                ),
                const SizedBox(height: 48),

                // Continue button
                ElevatedButton(
                  onPressed: () {
                    // Navigate to login page
                    Get.offAllNamed(AppRoutes.login);
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: ColorConstants.primary,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 48,
                      vertical: 16,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: const Text(
                    'Continue',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildLanguageOption(
    LanguageController controller,
    String language,
    String flagPath,
    String languageCode,
  ) {
    return Obx(
      () => GestureDetector(
        onTap: () => controller.changeLanguage(languageCode),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color:
                  controller.currentLanguage == languageCode
                      ? ColorConstants.primary
                      : Colors.grey.shade300,
              width: 2,
            ),
            color:
                controller.currentLanguage == languageCode
                    ? ColorConstants.primary.withAlpha(
                      25,
                    ) // Using withAlpha instead of withOpacity
                    : Colors.transparent,
          ),
          child: Column(
            children: [
              Image.asset(flagPath, width: 48, height: 48),
              const SizedBox(height: 8),
              Text(
                language,
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color:
                      controller.currentLanguage == languageCode
                          ? ColorConstants.primary
                          : Colors.black87,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
