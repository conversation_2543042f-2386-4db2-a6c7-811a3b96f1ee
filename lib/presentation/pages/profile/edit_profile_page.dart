import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/constants/color_constants.dart';
import '../../../core/utils/controller_utils.dart';
import '../../controllers/auth_controller.dart';
import '../../controllers/profile_controller.dart';
import '../../widgets/common/custom_button.dart';
import '../../widgets/common/custom_text_field.dart';
import '../../widgets/layout/responsive_sidebar.dart';

/// EditProfilePage for editing user profile
class EditProfilePage extends StatefulWidget {
  const EditProfilePage({super.key});

  @override
  State<EditProfilePage> createState() => _EditProfilePageState();
}

class _EditProfilePageState extends State<EditProfilePage> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _emailController = TextEditingController();
  final _phoneController = TextEditingController();
  final _addressController = TextEditingController();
  final _cityController = TextEditingController();

  late final ProfileController _profileController;
  late final AuthController _authController;

  @override
  void initState() {
    super.initState();
    _profileController = ControllerUtils.getProfileController();
    _authController = ControllerUtils.getAuthController();

    // Initialize form fields with user data
    final user = _authController.user;
    if (user != null) {
      _nameController.text = user.name;
      _emailController.text = user.email;
      _phoneController.text = user.phone ?? '';
      _addressController.text = user.address ?? '';
      _cityController.text = user.cityName ?? '';
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    _addressController.dispose();
    _cityController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ResponsiveSidebar(
      child: Scaffold(
        appBar: AppBar(
          title: Text('updateProfile'.tr),
          backgroundColor: ColorConstants.primary,
          elevation: 0,
          leading: IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: () => Get.back(),
          ),
        ),
        body: Obx(() {
          if (_profileController.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          return SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Form(
              key: _formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Profile image
                  Center(
                    child: Stack(
                      children: [
                        // Avatar
                        CircleAvatar(
                          radius: 60,
                          backgroundColor: ColorConstants.primary,
                          child: Text(
                            _nameController.text.isNotEmpty
                                ? _nameController.text[0].toUpperCase()
                                : 'U',
                            style: TextStyle(
                              color: ColorConstants.white,
                              fontWeight: FontWeight.bold,
                              fontSize: 40,
                            ),
                          ),
                        ),

                        // Edit icon
                        Positioned(
                          bottom: 0,
                          right: 0,
                          child: Container(
                            padding: const EdgeInsets.all(4),
                            decoration: BoxDecoration(
                              color: ColorConstants.primary,
                              shape: BoxShape.circle,
                              border: Border.all(color: Colors.white, width: 2),
                            ),
                            child: IconButton(
                              icon: const Icon(
                                Icons.camera_alt,
                                color: Colors.white,
                                size: 20,
                              ),
                              onPressed: () {
                                // TODO: Implement image upload
                                Get.snackbar(
                                  'updateProfile'.tr,
                                  'Feature coming soon'.tr,
                                  snackPosition: SnackPosition.BOTTOM,
                                  backgroundColor: ColorConstants.info
                                      .withAlpha(150),
                                  colorText: ColorConstants.white,
                                  margin: const EdgeInsets.all(16),
                                );
                              },
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 32),

                  // Form fields
                  Text(
                    'name'.tr,
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 8),
                  CustomTextField(
                    controller: _nameController,
                    hintText: 'name'.tr,
                    prefixIcon: Icons.person,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please enter your name'.tr;
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),

                  Text(
                    'email'.tr,
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 8),
                  CustomTextField(
                    controller: _emailController,
                    hintText: 'email'.tr,
                    prefixIcon: Icons.email,
                    enabled: false, // Email cannot be changed
                  ),
                  const SizedBox(height: 16),

                  Text(
                    'phoneNumber'.tr,
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 8),
                  CustomTextField(
                    controller: _phoneController,
                    hintText: 'phoneNumber'.tr,
                    prefixIcon: Icons.phone,
                    keyboardType: TextInputType.phone,
                    validator: (value) {
                      if (value != null &&
                          value.isNotEmpty &&
                          !GetUtils.isPhoneNumber(value)) {
                        return 'Please enter a valid phone number'.tr;
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),

                  Text(
                    'address'.tr,
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 8),
                  CustomTextField(
                    controller: _addressController,
                    hintText: 'address'.tr,
                    prefixIcon: Icons.location_on,
                  ),
                  const SizedBox(height: 16),

                  Text(
                    'city'.tr,
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 8),
                  CustomTextField(
                    controller: _cityController,
                    hintText: 'city'.tr,
                    prefixIcon: Icons.location_city,
                  ),
                  const SizedBox(height: 32),

                  // Save button
                  Center(
                    child: CustomButton(
                      text: 'save'.tr,
                      onPressed: _saveProfile,
                      isLoading: _profileController.isLoading,
                      width: 200,
                    ),
                  ),
                ],
              ),
            ),
          );
        }),
      ),
    );
  }

  /// Save profile changes
  Future<void> _saveProfile() async {
    if (_formKey.currentState!.validate()) {
      final user = _authController.user;
      if (user != null) {
        final updatedUser = user.copyWith(
          name: _nameController.text,
          phone: _phoneController.text,
          address: _addressController.text,
          cityName: _cityController.text,
        );

        final success = await _profileController.updateProfile(updatedUser);

        if (success) {
          Get.back();
          Get.snackbar(
            'updateProfile'.tr,
            'Profile updated successfully'.tr,
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: ColorConstants.success.withAlpha(150),
            colorText: ColorConstants.white,
            margin: const EdgeInsets.all(16),
          );
        } else {
          Get.snackbar(
            'Error'.tr,
            _profileController.errorMessage,
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: ColorConstants.error.withAlpha(150),
            colorText: ColorConstants.white,
            margin: const EdgeInsets.all(16),
          );
        }
      }
    }
  }
}
