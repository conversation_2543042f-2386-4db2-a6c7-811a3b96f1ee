import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/constants/color_constants.dart';
import '../../../core/utils/controller_utils.dart';
import '../../routes/app_routes.dart';
import '../../widgets/layout/responsive_sidebar.dart';

/// ProfilePage for user profile
class ProfilePage extends StatelessWidget {
  const ProfilePage({super.key});

  @override
  Widget build(BuildContext context) {
    final authController = ControllerUtils.getAuthController();
    final user = authController.user;

    if (user == null) {
      // If user is not logged in, redirect to login page
      WidgetsBinding.instance.addPostFrameCallback((_) {
        Get.offAllNamed(AppRoutes.login);
      });
      return const Scaffold(body: Center(child: CircularProgressIndicator()));
    }

    return ResponsiveSidebar(
      child: Scaffold(
        appBar: AppBar(
          title: Text('profile'.tr),
          backgroundColor: ColorConstants.primary,
          elevation: 0,
          actions: [
            IconButton(
              icon: const Icon(Icons.edit),
              onPressed: () {
                // Navigate to edit profile page
                Get.toNamed(AppRoutes.editProfile);
              },
            ),
          ],
        ),
        body: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Profile header
              Center(
                child: Column(
                  children: [
                    // User avatar
                    CircleAvatar(
                      radius: 50,
                      backgroundColor: ColorConstants.primary,
                      child: Text(
                        user.name.isNotEmpty ? user.name[0].toUpperCase() : 'U',
                        style: TextStyle(
                          color: ColorConstants.white,
                          fontWeight: FontWeight.bold,
                          fontSize: 36,
                        ),
                      ),
                    ),
                    const SizedBox(height: 16),

                    // User name
                    Text(
                      user.name,
                      style: const TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),

                    // User role
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 8,
                      ),
                      decoration: BoxDecoration(
                        color: ColorConstants.primary.withAlpha(50),
                        borderRadius: BorderRadius.circular(16),
                      ),
                      child: Text(
                        user.role ?? 'مستخدم',
                        style: TextStyle(
                          color: ColorConstants.primary,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 32),

              // Profile information
              _buildProfileSection(
                title: 'Account Information'.tr,
                icon: Icons.person,
                children: [
                  _buildProfileItem(
                    label: 'name'.tr,
                    value: user.name,
                    icon: Icons.person,
                  ),
                  _buildProfileItem(
                    label: 'email'.tr,
                    value: user.email,
                    icon: Icons.email,
                  ),
                  _buildProfileItem(
                    label: 'phoneNumber'.tr,
                    value: user.phone ?? 'Not Available'.tr,
                    icon: Icons.phone,
                  ),
                ],
              ),
              const SizedBox(height: 16),

              // Account actions
              _buildProfileSection(
                title: 'Account Actions'.tr,
                icon: Icons.settings,
                children: [
                  // Change password button
                  ListTile(
                    leading: Icon(Icons.lock, color: ColorConstants.primary),
                    title: Text('changePassword'.tr),
                    trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                    onTap: () {
                      // Navigate to change password page
                      Get.toNamed(AppRoutes.changePassword);
                    },
                  ),

                  // Logout button
                  ListTile(
                    leading: const Icon(Icons.logout, color: Colors.red),
                    title: Text(
                      'Logout'.tr,
                      style: const TextStyle(color: Colors.red),
                    ),
                    trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                    onTap: () async {
                      final success = await authController.logout();
                      if (success) {
                        Get.offAllNamed(AppRoutes.login);
                      }
                    },
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Build a profile section with a title, icon, and children widgets
  Widget _buildProfileSection({
    required String title,
    required IconData icon,
    required List<Widget> children,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withAlpha(50),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section header
          Row(
            children: [
              Icon(icon, color: ColorConstants.primary),
              const SizedBox(width: 8),
              Text(
                title,
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const Divider(),
          const SizedBox(height: 8),

          // Section content
          ...children,
        ],
      ),
    );
  }

  /// Build a profile item with a label, value, and icon
  Widget _buildProfileItem({
    required String label,
    required String value,
    required IconData icon,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Row(
        children: [
          Icon(icon, color: ColorConstants.primary, size: 20),
          const SizedBox(width: 8),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: TextStyle(color: Colors.grey.shade600, fontSize: 12),
              ),
              Text(
                value,
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
