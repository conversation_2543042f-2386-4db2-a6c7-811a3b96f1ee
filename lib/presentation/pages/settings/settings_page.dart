import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/constants/color_constants.dart';
import '../../../core/utils/controller_utils.dart';
import '../../widgets/common/language_selector.dart';
import '../../widgets/layout/responsive_sidebar.dart';

/// SettingsPage for app settings
class SettingsPage extends StatelessWidget {
  const SettingsPage({super.key});

  @override
  Widget build(BuildContext context) {
    final languageController = ControllerUtils.getLanguageController();
    final themeController = ControllerUtils.getThemeController();

    return ResponsiveSidebar(
      child: Scaffold(
        appBar: AppBar(
          title: const Text('الإعدادات'),
          backgroundColor: ColorConstants.primary,
          elevation: 0,
        ),
        body: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Settings header
              const Text(
                'إعدادات التطبيق',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 24),

              // Language settings
              _buildSettingsSection(
                title: 'اللغة',
                icon: Icons.language,
                child: Column(
                  children: [
                    // Language selector
                    Row(
                      children: [
                        const Text('اختر اللغة:'),
                        const SizedBox(width: 16),
                        const LanguageSelector(),
                        const Spacer(),
                        Obx(
                          () => Text(
                            languageController.isArabic ? 'العربية' : 'English',
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),

                    // Language toggle button
                    ElevatedButton.icon(
                      onPressed: () {
                        languageController.toggleLanguage();
                      },
                      icon: const Icon(Icons.swap_horiz),
                      label: const Text('تبديل اللغة'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: ColorConstants.primary,
                        foregroundColor: Colors.white,
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 16),

              // Theme settings
              _buildSettingsSection(
                title: 'المظهر',
                icon: Icons.palette,
                child: Column(
                  children: [
                    // Theme mode selector
                    Row(
                      children: [
                        const Text('وضع المظهر:'),
                        const SizedBox(width: 16),
                        Obx(
                          () => Switch(
                            value: themeController.isDarkMode,
                            onChanged: (value) {
                              themeController.toggleTheme();
                            },
                            activeColor: ColorConstants.primary,
                          ),
                        ),
                        const SizedBox(width: 8),
                        Obx(
                          () => Text(
                            themeController.isDarkMode
                                ? 'الوضع الداكن'
                                : 'الوضع الفاتح',
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 16),

              // Notification settings
              _buildSettingsSection(
                title: 'الإشعارات',
                icon: Icons.notifications,
                child: Column(
                  children: [
                    // Push notifications
                    Row(
                      children: [
                        const Text('إشعارات الدفع:'),
                        const SizedBox(width: 16),
                        Switch(
                          value: true,
                          onChanged: (value) {
                            // Toggle push notifications
                          },
                          activeColor: ColorConstants.primary,
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),

                    // Email notifications
                    Row(
                      children: [
                        const Text('إشعارات البريد الإلكتروني:'),
                        const SizedBox(width: 16),
                        Switch(
                          value: false,
                          onChanged: (value) {
                            // Toggle email notifications
                          },
                          activeColor: ColorConstants.primary,
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 16),

              // About section
              _buildSettingsSection(
                title: 'حول التطبيق',
                icon: Icons.info,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text('إصدار التطبيق: 1.0.0'),
                    const SizedBox(height: 8),
                    const Text('تم التطوير بواسطة: فريق بساطي'),
                    const SizedBox(height: 16),
                    TextButton.icon(
                      onPressed: () {
                        // Show about dialog
                        showAboutDialog(
                          context: context,
                          applicationName: 'بساطي',
                          applicationVersion: '1.0.0',
                          applicationIcon: Icon(
                            Icons.directions_bus_rounded,
                            color: ColorConstants.primary,
                            size: 32,
                          ),
                          applicationLegalese:
                              'جميع الحقوق محفوظة © 2023 بساطي',
                        );
                      },
                      icon: const Icon(Icons.more_horiz),
                      label: const Text('عرض المزيد'),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Build a settings section with a title, icon, and child widget
  Widget _buildSettingsSection({
    required String title,
    required IconData icon,
    required Widget child,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withAlpha(50),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section header
          Row(
            children: [
              Icon(
                icon,
                color: ColorConstants.primary,
              ),
              const SizedBox(width: 8),
              Text(
                title,
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const Divider(),
          const SizedBox(height: 8),

          // Section content
          child,
        ],
      ),
    );
  }
}
