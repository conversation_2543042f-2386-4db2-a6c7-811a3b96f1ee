import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../core/utils/responsive_utils.dart';
import '../../../domain/entities/student.dart';
import '../../controllers/students_controller.dart';
import '../../widgets/common/custom_app_bar.dart';
import '../../widgets/common/custom_button.dart';
import '../../widgets/common/custom_text_field.dart';

/// Edit Student Page
class EditStudentPage extends StatelessWidget {
  const EditStudentPage({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<StudentsController>();
    final Student? student = Get.arguments as Student?;
    final isDesktop = ResponsiveUtils.isDesktop(context);

    // Initialize form with student data
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (student != null) {
        controller.initializeEditForm(student);
      }
    });

    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      appBar: CustomAppBar(
        title: 'editStudent'.tr,
        showBackButton: true,
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(isDesktop ? 32 : 16),
        child: Center(
          child: Container(
            constraints: BoxConstraints(
              maxWidth: isDesktop ? 800 : double.infinity,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header Section
                _buildHeader(context, isDesktop, student),

                SizedBox(height: isDesktop ? 32 : 24),

                // Form Section
                _buildForm(context, controller, isDesktop),

                SizedBox(height: isDesktop ? 40 : 32),

                // Action Buttons
                _buildActionButtons(context, controller, isDesktop, student),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context, bool isDesktop, Student? student) {
    return Container(
      padding: EdgeInsets.all(isDesktop ? 32 : 24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.orange,
            Colors.orange.withValues(alpha: 0.8),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.orange.withValues(alpha: 0.3),
            blurRadius: 20,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            padding: EdgeInsets.all(isDesktop ? 20 : 16),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: Colors.white.withValues(alpha: 0.3),
                width: 1,
              ),
            ),
            child: Icon(
              Icons.edit_rounded,
              color: Colors.white,
              size: isDesktop ? 40 : 32,
            ),
          ),
          SizedBox(width: isDesktop ? 24 : 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'editStudent'.tr,
                  style: TextStyle(
                    fontSize: isDesktop ? 28 : 24,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                SizedBox(height: isDesktop ? 8 : 4),
                Text(
                  'Editing: ${student?.name ?? 'Unknown Student'}',
                  style: TextStyle(
                    fontSize: isDesktop ? 16 : 14,
                    color: Colors.white.withValues(alpha: 0.9),
                    height: 1.4,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildForm(BuildContext context, StudentsController controller, bool isDesktop) {
    return Container(
      padding: EdgeInsets.all(isDesktop ? 32 : 24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.08),
            spreadRadius: 0,
            blurRadius: 20,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Form(
        key: controller.editStudentFormKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'studentData'.tr,
              style: TextStyle(
                fontSize: isDesktop ? 20 : 18,
                fontWeight: FontWeight.bold,
                color: Colors.orange,
              ),
            ),

            SizedBox(height: isDesktop ? 24 : 20),

            // Student Name
            CustomTextField(
              controller: controller.nameController,
              labelText: 'studentName'.tr,
              hintText: 'Enter student full name',
              prefixIcon: Icons.person_rounded,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'validName'.tr;
                }
                return null;
              },
            ),

            SizedBox(height: isDesktop ? 20 : 16),

            // Gender Selection
            CustomTextField(
              controller: controller.genderController,
              labelText: 'gender'.tr,
              hintText: 'Select gender',
              prefixIcon: Icons.person_rounded,
              readOnly: true,
              onTap: () => _showGenderSelection(context, controller),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Gender ${'isRequired'.tr}';
                }
                return null;
              },
            ),

            SizedBox(height: isDesktop ? 20 : 16),

            // Religion Selection
            CustomTextField(
              controller: controller.religionController,
              labelText: 'religion'.tr,
              hintText: 'Select religion',
              prefixIcon: Icons.mosque_rounded,
              readOnly: true,
              onTap: () => _showReligionSelection(context, controller),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Religion ${'isRequired'.tr}';
                }
                return null;
              },
            ),

            SizedBox(height: isDesktop ? 20 : 16),

            // Grade Selection
            CustomTextField(
              controller: controller.gradeController,
              labelText: 'grade'.tr,
              hintText: 'Select grade',
              prefixIcon: Icons.school_rounded,
              readOnly: true,
              onTap: () => _showGradeSelection(context, controller),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Grade ${'isRequired'.tr}';
                }
                return null;
              },
            ),

            SizedBox(height: isDesktop ? 20 : 16),

            // Phone Number
            CustomTextField(
              controller: controller.phoneController,
              labelText: 'phone'.tr,
              hintText: 'Enter phone number',
              prefixIcon: Icons.phone_rounded,
              keyboardType: TextInputType.phone,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'validPhone'.tr;
                }
                return null;
              },
            ),

            SizedBox(height: isDesktop ? 20 : 16),

            // Address
            CustomTextField(
              controller: controller.addressController,
              labelText: 'address'.tr,
              hintText: 'Enter student address',
              prefixIcon: Icons.location_on_rounded,
              maxLines: 3,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'validAddress'.tr;
                }
                return null;
              },
            ),

            SizedBox(height: isDesktop ? 20 : 16),

            // City Name
            CustomTextField(
              controller: controller.cityNameController,
              labelText: 'cityName'.tr,
              hintText: 'Enter city name',
              prefixIcon: Icons.location_city_rounded,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'City name ${'isRequired'.tr}';
                }
                return null;
              },
            ),

            SizedBox(height: isDesktop ? 20 : 16),

            // Date of Birth
            CustomTextField(
              controller: controller.dateBirthController,
              labelText: 'dateOfBirth'.tr,
              hintText: 'Select date of birth',
              prefixIcon: Icons.calendar_today_rounded,
              readOnly: true,
              onTap: () => _selectDate(context, controller),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Date of birth ${'isRequired'.tr}';
                }
                return null;
              },
            ),

            SizedBox(height: isDesktop ? 20 : 16),

            // Blood Type Selection
            CustomTextField(
              controller: controller.typeBloodController,
              labelText: 'bloodType'.tr,
              hintText: 'Select blood type',
              prefixIcon: Icons.bloodtype_rounded,
              readOnly: true,
              onTap: () => _showBloodTypeSelection(context, controller),
            ),

            SizedBox(height: isDesktop ? 20 : 16),

            // Trip Type Selection
            CustomTextField(
              controller: controller.tripTypeController,
              labelText: 'tripType'.tr,
              hintText: 'Select trip type',
              prefixIcon: Icons.directions_bus_rounded,
              readOnly: true,
              onTap: () => _showTripTypeSelection(context, controller),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Trip type ${'isRequired'.tr}';
                }
                return null;
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons(BuildContext context, StudentsController controller, bool isDesktop, Student? student) {
    return Row(
      children: [
        Expanded(
          child: CustomButton(
            text: 'cancel'.tr,
            onPressed: () => Get.back(),
            backgroundColor: Colors.grey.shade300,
            textColor: Colors.grey.shade700,
            borderRadius: 12,
            height: isDesktop ? 56 : 48,
          ),
        ),
        SizedBox(width: isDesktop ? 20 : 16),
        Expanded(
          child: Obx(() => CustomButton(
            text: 'save'.tr,
            onPressed: controller.isLoading ? null : () => controller.updateStudent(student?.id),
            isLoading: controller.isLoading,
            backgroundColor: Colors.orange,
            borderRadius: 12,
            height: isDesktop ? 56 : 48,
          )),
        ),
      ],
    );
  }

  Future<void> _selectDate(BuildContext context, StudentsController controller) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now().subtract(const Duration(days: 365 * 10)), // 10 years ago
      firstDate: DateTime.now().subtract(const Duration(days: 365 * 25)), // 25 years ago
      lastDate: DateTime.now(),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: ColorScheme.light(
              primary: Colors.orange,
              onPrimary: Colors.white,
              surface: Colors.white,
              onSurface: Colors.black,
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null) {
      controller.dateBirthController.text = "${picked.day}/${picked.month}/${picked.year}";
    }
  }

  void _showGenderSelection(BuildContext context, StudentsController controller) {
    final genderOptions = [
      {'id': 1, 'name': 'Male'},
      {'id': 2, 'name': 'Female'},
    ];

    Get.dialog(
      Dialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        child: Container(
          padding: const EdgeInsets.all(24),
          constraints: const BoxConstraints(maxWidth: 400),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'Select Gender',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.orange,
                ),
              ),
              const SizedBox(height: 20),
              ...genderOptions.map((gender) => ListTile(
                title: Text(gender['name'] as String),
                onTap: () {
                  controller.genderController.text = gender['name'] as String;
                  controller.selectedGenderId.value = gender['id'] as int;
                  Get.back();
                },
              )),
              const SizedBox(height: 16),
              TextButton(
                onPressed: () => Get.back(),
                child: const Text('Cancel'),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showReligionSelection(BuildContext context, StudentsController controller) {
    final religionOptions = [
      {'id': 1, 'name': 'Islam'},
      {'id': 2, 'name': 'Christianity'},
      {'id': 3, 'name': 'Judaism'},
      {'id': 4, 'name': 'Other'},
    ];

    Get.dialog(
      Dialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        child: Container(
          padding: const EdgeInsets.all(24),
          constraints: const BoxConstraints(maxWidth: 400),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'Select Religion',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.orange,
                ),
              ),
              const SizedBox(height: 20),
              ...religionOptions.map((religion) => ListTile(
                title: Text(religion['name'] as String),
                onTap: () {
                  controller.religionController.text = religion['name'] as String;
                  controller.selectedReligionId.value = religion['id'] as int;
                  Get.back();
                },
              )),
              const SizedBox(height: 16),
              TextButton(
                onPressed: () => Get.back(),
                child: const Text('Cancel'),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showGradeSelection(BuildContext context, StudentsController controller) {
    final gradeOptions = [
      {'id': 1, 'name': 'Grade 1'},
      {'id': 2, 'name': 'Grade 2'},
      {'id': 3, 'name': 'Grade 3'},
      {'id': 4, 'name': 'Grade 4'},
      {'id': 5, 'name': 'Grade 5'},
      {'id': 6, 'name': 'Grade 6'},
      {'id': 7, 'name': 'Grade 7'},
      {'id': 8, 'name': 'Grade 8'},
      {'id': 9, 'name': 'Grade 9'},
      {'id': 10, 'name': 'Grade 10'},
      {'id': 11, 'name': 'Grade 11'},
      {'id': 12, 'name': 'Grade 12'},
    ];

    Get.dialog(
      Dialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        child: Container(
          padding: const EdgeInsets.all(24),
          constraints: const BoxConstraints(maxWidth: 400, maxHeight: 500),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'Select Grade',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.orange,
                ),
              ),
              const SizedBox(height: 20),
              Expanded(
                child: ListView(
                  children: gradeOptions.map((grade) => ListTile(
                    title: Text(grade['name'] as String),
                    onTap: () {
                      controller.gradeController.text = grade['name'] as String;
                      controller.selectedGradeId.value = grade['id'] as int;
                      Get.back();
                    },
                  )).toList(),
                ),
              ),
              const SizedBox(height: 16),
              TextButton(
                onPressed: () => Get.back(),
                child: const Text('Cancel'),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showBloodTypeSelection(BuildContext context, StudentsController controller) {
    final bloodTypeOptions = [
      {'id': 1, 'name': 'A+'},
      {'id': 2, 'name': 'A-'},
      {'id': 3, 'name': 'B+'},
      {'id': 4, 'name': 'B-'},
      {'id': 5, 'name': 'AB+'},
      {'id': 6, 'name': 'AB-'},
      {'id': 7, 'name': 'O+'},
      {'id': 8, 'name': 'O-'},
    ];

    Get.dialog(
      Dialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        child: Container(
          padding: const EdgeInsets.all(24),
          constraints: const BoxConstraints(maxWidth: 400),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'Select Blood Type',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.orange,
                ),
              ),
              const SizedBox(height: 20),
              ...bloodTypeOptions.map((bloodType) => ListTile(
                title: Text(bloodType['name'] as String),
                onTap: () {
                  controller.typeBloodController.text = bloodType['name'] as String;
                  controller.selectedTypeBloodId.value = bloodType['id'] as int;
                  Get.back();
                },
              )),
              const SizedBox(height: 16),
              TextButton(
                onPressed: () => Get.back(),
                child: const Text('Cancel'),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showTripTypeSelection(BuildContext context, StudentsController controller) {
    final tripTypeOptions = [
      {'value': 'morning', 'name': 'Morning Only'},
      {'value': 'evening', 'name': 'Evening Only'},
      {'value': 'full_day', 'name': 'Full Day'},
    ];

    Get.dialog(
      Dialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        child: Container(
          padding: const EdgeInsets.all(24),
          constraints: const BoxConstraints(maxWidth: 400),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'Select Trip Type',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.orange,
                ),
              ),
              const SizedBox(height: 20),
              ...tripTypeOptions.map((tripType) => ListTile(
                title: Text(tripType['name'] as String),
                onTap: () {
                  controller.tripTypeController.text = tripType['name'] as String;
                  Get.back();
                },
              )),
              const SizedBox(height: 16),
              TextButton(
                onPressed: () => Get.back(),
                child: const Text('Cancel'),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
