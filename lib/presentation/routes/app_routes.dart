/// AppRoutes class for defining route names
/// Following Single Responsibility Principle by focusing only on route names
class AppRoutes {
  // Private constructor to prevent instantiation
  AppRoutes._();

  // Main routes
  static const String initial = '/';
  static const String splash = '/splash';
  static const String home = '/home';
  static const String homePage = '/home-page';

  // Authentication routes
  static const String login = '/login';
  static const String register = '/register';
  static const String forgotPassword = '/forgot-password';
  static const String resetPassword = '/reset-password';
  static const String verifyEmail = '/verify-email';

  // User management routes
  static const String profile = '/profile';
  static const String editProfile = '/profile/edit';
  static const String changePassword = '/change-password';

  // Parent management routes
  static const String parents = '/parents';
  static const String parentDetails = '/parents/details';
  static const String addParent = '/parents/add';
  static const String editParent = '/parents/edit';

  // Student management routes
  static const String students = '/students';
  static const String studentDetails = '/students/details';
  static const String addStudent = '/students/add';
  static const String editStudent = '/students/edit';

  // Bus management routes
  static const String buses = '/buses';
  static const String busDetails = '/buses/details';
  static const String addBus = '/buses/add';
  static const String editBus = '/buses/edit';

  // Driver management routes
  static const String drivers = '/drivers';
  static const String driverDetails = '/drivers/details';
  static const String addDriver = '/drivers/add';
  static const String editDriver = '/drivers/edit';

  // Supervisor management routes
  static const String supervisors = '/supervisors';
  static const String supervisorDetails = '/supervisors/details';
  static const String addSupervisor = '/supervisors/add';
  static const String editSupervisor = '/supervisors/edit';

  // Trip management routes
  static const String trips = '/trips';
  static const String tripDetails = '/trips/details';
  static const String addTrip = '/trips/add';
  static const String editTrip = '/trips/edit';
  static const String trackTrip = '/trips/track';

  // Attendance management routes
  static const String attendance = '/attendance';
  static const String attendanceDetails = '/attendance/details';
  static const String markAttendance = '/attendance/mark';

  // Settings routes
  static const String settings = '/settings';
  static const String languageSelection = '/language-selection';

  // Address change request routes
  static const String addressChangeRequests = '/address-change-requests';
  static const String addressChangeRequestDetails =
      '/address-change-requests/details';

  // Previous trips routes
  static const String previousTrips = '/previous-trips';
  static const String previousTripDetails = '/previous-trips/details';

  // Debug routes
  static const String busApiDebug = '/debug/bus-api';
}
