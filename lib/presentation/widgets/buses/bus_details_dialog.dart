import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../core/constants/color_constants.dart';
import '../../../core/utils/responsive_utils.dart';
import '../../../domain/entities/bus.dart';
import '../../routes/app_routes.dart';

/// Bus details dialog widget
/// Following Single Responsibility Principle by focusing only on bus details UI
class BusDetailsDialog extends StatelessWidget {
  final Bus bus;

  const BusDetailsDialog({
    super.key,
    required this.bus,
  });

  @override
  Widget build(BuildContext context) {
    final isDesktop = ResponsiveUtils.isDesktop(context);

    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        width: isDesktop ? 600 : double.infinity,
        constraints: BoxConstraints(
          maxHeight: MediaQuery.of(context).size.height * 0.8,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            _buildHeader(context, isDesktop),

            // Content
            Flexible(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(24),
                child: _buildContent(context, isDesktop),
              ),
            ),

            // Actions
            _buildActions(context, isDesktop),
          ],
        ),
      ),
    );
  }

  /// Build header
  Widget _buildHeader(BuildContext context, bool isDesktop) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: ColorConstants.primary.withValues(alpha: 0.05),
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(16),
          topRight: Radius.circular(16),
        ),
      ),
      child: Row(
        children: [
          // Bus icon
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: ColorConstants.primary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(
              Icons.directions_bus_rounded,
              color: ColorConstants.primary,
              size: isDesktop ? 28 : 24,
            ),
          ),
          const SizedBox(width: 16),

          // Title and subtitle
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  bus.name ?? 'Bus Details',
                  style: TextStyle(
                    fontSize: isDesktop ? 24 : 20,
                    fontWeight: FontWeight.bold,
                    color: ColorConstants.textPrimary,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'Bus Information',
                  style: TextStyle(
                    fontSize: isDesktop ? 16 : 14,
                    color: ColorConstants.textSecondary,
                  ),
                ),
              ],
            ),
          ),

          // Close button
          IconButton(
            onPressed: () => Navigator.of(context).pop(),
            icon: Icon(
              Icons.close_rounded,
              color: ColorConstants.textSecondary,
            ),
          ),
        ],
      ),
    );
  }

  /// Build content
  Widget _buildContent(BuildContext context, bool isDesktop) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Basic Information Section
        _buildSection(
          title: 'Basic Information',
          icon: Icons.info_outline_rounded,
          children: [
            _buildDetailRow('Bus Name', bus.name ?? 'N/A'),
            _buildDetailRow('Car Number', bus.carNumber ?? 'N/A'),
            _buildDetailRow('Notes', bus.notes ?? 'No notes available'),
          ],
        ),
        const SizedBox(height: 24),

        // System Information Section
        _buildSection(
          title: 'System Information',
          icon: Icons.settings_outlined,
          children: [
            _buildDetailRow('Bus ID', bus.id?.toString() ?? 'N/A'),
            _buildDetailRow('Created Date', _formatDate(bus.createdAt)),
            _buildDetailRow('Last Updated', _formatDate(bus.updatedAt)),
          ],
        ),
        const SizedBox(height: 24),

        // Status Section (Mock data for demonstration)
        _buildSection(
          title: 'Status & Statistics',
          icon: Icons.analytics_outlined,
          children: [
            _buildDetailRow('Status', 'Active', valueColor: Colors.green),
            _buildDetailRow('Total Students', '25', valueColor: ColorConstants.primary),
            _buildDetailRow('Current Route', 'Route A - Morning', valueColor: ColorConstants.primary),
            _buildDetailRow('Last Trip', '2 hours ago', valueColor: ColorConstants.textSecondary),
          ],
        ),
      ],
    );
  }

  /// Build section
  Widget _buildSection({
    required String title,
    required IconData icon,
    required List<Widget> children,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Section header
        Row(
          children: [
            Icon(
              icon,
              color: ColorConstants.primary,
              size: 20,
            ),
            const SizedBox(width: 8),
            Text(
              title,
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: ColorConstants.textPrimary,
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),

        // Section content
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.grey.shade50,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: Colors.grey.shade200,
              width: 1,
            ),
          ),
          child: Column(
            children: children,
          ),
        ),
      ],
    );
  }

  /// Build detail row
  Widget _buildDetailRow(String label, String value, {Color? valueColor}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Label
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: TextStyle(
                fontWeight: FontWeight.w500,
                color: ColorConstants.textSecondary,
                fontSize: 14,
              ),
            ),
          ),

          // Separator
          Text(
            ': ',
            style: TextStyle(
              color: ColorConstants.textSecondary,
              fontSize: 14,
            ),
          ),

          // Value
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                color: valueColor ?? ColorConstants.textPrimary,
                fontSize: 14,
                fontWeight: valueColor != null ? FontWeight.w500 : FontWeight.normal,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Build actions
  Widget _buildActions(BuildContext context, bool isDesktop) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(16),
          bottomRight: Radius.circular(16),
        ),
      ),
      child: Row(
        children: [
          // Edit button
          Expanded(
            child: OutlinedButton.icon(
              onPressed: () {
                Navigator.of(context).pop();
                Get.toNamed(AppRoutes.editBus, arguments: bus);
              },
              icon: const Icon(Icons.edit_outlined),
              label: const Text('Edit Bus'),
              style: OutlinedButton.styleFrom(
                foregroundColor: ColorConstants.primary,
                side: BorderSide(color: ColorConstants.primary),
                padding: const EdgeInsets.symmetric(vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
          ),
          const SizedBox(width: 16),

          // View students button
          Expanded(
            child: ElevatedButton.icon(
              onPressed: () {
                Navigator.of(context).pop();
                // TODO: Navigate to students page filtered by this bus
                Get.toNamed(AppRoutes.students, parameters: {'busId': bus.id.toString()});
              },
              icon: const Icon(Icons.people_rounded),
              label: const Text('View Students'),
              style: ElevatedButton.styleFrom(
                backgroundColor: ColorConstants.primary,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Format date string
  String _formatDate(String? dateString) {
    if (dateString == null) return 'N/A';

    try {
      final date = DateTime.parse(dateString);
      return '${date.day}/${date.month}/${date.year} at ${date.hour}:${date.minute.toString().padLeft(2, '0')}';
    } catch (e) {
      return 'N/A';
    }
  }
}
