import 'package:flutter/material.dart';
import '../../../core/constants/color_constants.dart';

/// CustomButton widget for consistent button styling
/// Following Single Responsibility Principle by focusing only on button UI
class CustomButton extends StatelessWidget {
  final String text;
  final VoidCallback? onPressed;
  final bool isLoading;
  final bool isOutlined;
  final Color? backgroundColor;
  final Color? textColor;
  final double? width;
  final double? height;
  final double borderRadius;
  final IconData? icon;
  final bool iconAfterText;

  const CustomButton({
    Key? key,
    required this.text,
    required this.onPressed,
    this.isLoading = false,
    this.isOutlined = false,
    this.backgroundColor,
    this.textColor,
    this.width,
    this.height = 50,
    this.borderRadius = 8,
    this.icon,
    this.iconAfterText = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: width ?? double.infinity,
      height: height,
      child: ElevatedButton(
        onPressed: isLoading ? null : onPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: isOutlined
              ? Colors.transparent
              : backgroundColor ?? ColorConstants.primary,
          foregroundColor: isOutlined
              ? textColor ?? ColorConstants.primary
              : textColor ?? ColorConstants.white,
          elevation: isOutlined ? 0 : 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(borderRadius),
            side: isOutlined
                ? BorderSide(
                    color: backgroundColor ?? ColorConstants.primary,
                    width: 1,
                  )
                : BorderSide.none,
          ),
          padding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 12,
          ),
          disabledBackgroundColor: isOutlined
              ? Colors.transparent
              : ColorConstants.primary.withAlpha(150),
          disabledForegroundColor: isOutlined
              ? ColorConstants.primary.withAlpha(150)
              : ColorConstants.white.withAlpha(150),
        ),
        child: isLoading
            ? SizedBox(
                width: 24,
                height: 24,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    isOutlined
                        ? ColorConstants.primary
                        : ColorConstants.white,
                  ),
                ),
              )
            : _buildButtonContent(),
      ),
    );
  }

  Widget _buildButtonContent() {
    if (icon == null) {
      return Text(
        text,
        style: TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.bold,
        ),
      );
    }

    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        if (!iconAfterText) ...[
          Icon(icon),
          const SizedBox(width: 8),
        ],
        Text(
          text,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        if (iconAfterText) ...[
          const SizedBox(width: 8),
          Icon(icon),
        ],
      ],
    );
  }
}
