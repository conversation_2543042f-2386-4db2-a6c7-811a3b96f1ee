import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/localization/language_controller.dart';
import '../../../core/utils/controller_utils.dart';

/// LanguageSelector widget for selecting language
/// This widget provides a dropdown to select between English and Arabic
class LanguageSelector extends StatelessWidget {
  final bool isCompact;

  const LanguageSelector({super.key, this.isCompact = false});

  @override
  Widget build(BuildContext context) {
    // Get the language controller safely using our utility function
    final LanguageController languageController =
        ControllerUtils.getLanguageController();

    return Obx(
      () =>
          isCompact
              ? _buildCompactSelector(languageController)
              : _buildFullSelector(languageController),
    );
  }

  Widget _buildFullSelector(LanguageController controller) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: DropdownButton<String>(
        value: controller.currentLanguage,
        underline: const SizedBox(),
        icon: const Icon(Icons.language),
        items: [
          DropdownMenuItem(
            value: 'en',
            child: Row(
              children: [
                Image.asset('assets/images/gb.png', width: 24, height: 24),
                const SizedBox(width: 8),
                const Text('English'),
              ],
            ),
          ),
          DropdownMenuItem(
            value: 'ar',
            child: Row(
              children: [
                Image.asset('assets/images/ar.png', width: 24, height: 24),
                const SizedBox(width: 8),
                const Text('العربية'),
              ],
            ),
          ),
        ],
        onChanged: (value) {
          if (value != null) {
            controller.changeLanguage(value);
          }
        },
      ),
    );
  }

  Widget _buildCompactSelector(LanguageController controller) {
    return IconButton(
      icon: const Icon(Icons.language),
      tooltip: 'languages'.tr,
      onPressed: () {
        controller.toggleLanguage();
      },
    );
  }
}
