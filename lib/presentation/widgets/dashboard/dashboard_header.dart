import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/constants/color_constants.dart';
import '../../../core/utils/responsive_utils.dart';
import '../../controllers/auth_controller.dart';
import '../../controllers/dashboard_controller.dart';
import '../../routes/app_routes.dart';

/// DashboardHeader widget for dashboard page header
/// Following Single Responsibility Principle by focusing only on header UI
class DashboardHeader extends StatelessWidget {
  const DashboardHeader({super.key});

  @override
  Widget build(BuildContext context) {
    final authController = Get.find<AuthController>();
    final user = authController.user;

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        // Welcome message
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'مرحباً، ${user?.name ?? 'مستخدم'}',
              style: TextStyle(
                fontSize: ResponsiveUtils.getResponsiveFontSizeByDevice(
                  context,
                  mobile: 20,
                  tablet: 24,
                  desktop: 28,
                ),
                fontWeight: FontWeight.bold,
                color: ColorConstants.text,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              'مرحباً بك في لوحة التحكم',
              style: TextStyle(
                fontSize: ResponsiveUtils.getResponsiveFontSizeByDevice(
                  context,
                  mobile: 14,
                  tablet: 16,
                  desktop: 18,
                ),
                color: Colors.black54,
              ),
            ),
          ],
        ),

        // Action buttons
        Row(
          children: [
            // Home page button
            IconButton(
              onPressed: () {
                // Navigate to home page
                Get.toNamed(AppRoutes.homePage);
              },
              icon: Icon(
                Icons.home_rounded,
                color: ColorConstants.primary,
                size: ResponsiveUtils.getResponsiveIconSize(
                  context,
                  mobile: 24,
                  tablet: 28,
                  desktop: 32,
                ),
              ),
              tooltip: 'الصفحة الرئيسية',
            ),

            // Refresh button
            IconButton(
              onPressed: () {
                // Refresh dashboard data
                Get.find<DashboardController>().refreshDashboardData();
              },
              icon: Icon(
                Icons.refresh_rounded,
                color: ColorConstants.primary,
                size: ResponsiveUtils.getResponsiveIconSize(
                  context,
                  mobile: 24,
                  tablet: 28,
                  desktop: 32,
                ),
              ),
              tooltip: 'تحديث',
            ),

            // Notifications button
            IconButton(
              onPressed: () {
                // Show notifications
              },
              icon: Icon(
                Icons.notifications_outlined,
                color: ColorConstants.primary,
                size: ResponsiveUtils.getResponsiveIconSize(
                  context,
                  mobile: 24,
                  tablet: 28,
                  desktop: 32,
                ),
              ),
              tooltip: 'الإشعارات',
            ),

            // Profile button
            InkWell(
              onTap: () {
                // Navigate to profile
                Get.toNamed('/profile');
              },
              borderRadius: BorderRadius.circular(50),
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: Row(
                  children: [
                    // Profile image
                    CircleAvatar(
                      radius: ResponsiveUtils.getResponsiveValue<double>(
                        context: context,
                        mobile: 16,
                        tablet: 20,
                        desktop: 24,
                      ),
                      backgroundColor: ColorConstants.primary.withAlpha(26),
                      child: Text(
                        user != null && user.name.isNotEmpty
                            ? user.name[0].toUpperCase()
                            : 'U',
                        style: TextStyle(
                          color: ColorConstants.primary,
                          fontWeight: FontWeight.bold,
                          fontSize:
                              ResponsiveUtils.getResponsiveFontSizeByDevice(
                                context,
                                mobile: 14,
                                tablet: 16,
                                desktop: 18,
                              ),
                        ),
                      ),
                    ),

                    // Only show name on tablet and desktop
                    if (ResponsiveUtils.getDeviceType(context) !=
                        DeviceScreenType.mobile) ...[
                      const SizedBox(width: 8),
                      Text(
                        user?.name ?? 'مستخدم',
                        style: TextStyle(
                          fontSize:
                              ResponsiveUtils.getResponsiveFontSizeByDevice(
                                context,
                                mobile: 14,
                                tablet: 16,
                                desktop: 18,
                              ),
                          fontWeight: FontWeight.w500,
                          color: ColorConstants.text,
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }
}
