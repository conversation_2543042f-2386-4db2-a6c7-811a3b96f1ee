import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import '../../../core/constants/color_constants.dart';
import '../../../core/utils/responsive_utils.dart';
import '../../../domain/entities/school_stats.dart';

/// SchoolStatsChart widget for displaying school statistics chart
/// Following Single Responsibility Principle by focusing only on chart UI
class SchoolStatsChart extends StatelessWidget {
  final List<SchoolStats> schoolStats;

  const SchoolStatsChart({super.key, required this.schoolStats});

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: BorderSide(color: Colors.grey.shade200, width: 1),
      ),
      color: Colors.white,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'إحصائيات المدرسة',
                  style: TextStyle(
                    fontSize: ResponsiveUtils.getResponsiveFontSizeByDevice(
                      context,
                      mobile: 16,
                      tablet: 18,
                      desktop: 20,
                    ),
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                  ),
                ),

                // Filter dropdown
                DropdownButton<String>(
                  value: 'this_month',
                  underline: const SizedBox(),
                  icon: Icon(
                    Icons.keyboard_arrow_down_rounded,
                    color: ColorConstants.primary,
                  ),
                  items: [
                    DropdownMenuItem(
                      value: 'this_week',
                      child: Text(
                        'هذا الأسبوع',
                        style: TextStyle(
                          fontSize:
                              ResponsiveUtils.getResponsiveFontSizeByDevice(
                                context,
                                mobile: 12,
                                tablet: 14,
                                desktop: 16,
                              ),
                          color: Colors.black87,
                        ),
                      ),
                    ),
                    DropdownMenuItem(
                      value: 'this_month',
                      child: Text(
                        'هذا الشهر',
                        style: TextStyle(
                          fontSize:
                              ResponsiveUtils.getResponsiveFontSizeByDevice(
                                context,
                                mobile: 12,
                                tablet: 14,
                                desktop: 16,
                              ),
                          color: Colors.black87,
                        ),
                      ),
                    ),
                    DropdownMenuItem(
                      value: 'this_year',
                      child: Text(
                        'هذا العام',
                        style: TextStyle(
                          fontSize:
                              ResponsiveUtils.getResponsiveFontSizeByDevice(
                                context,
                                mobile: 12,
                                tablet: 14,
                                desktop: 16,
                              ),
                          color: Colors.black87,
                        ),
                      ),
                    ),
                  ],
                  onChanged: (value) {
                    // Handle filter change
                  },
                ),
              ],
            ),
            const SizedBox(height: 24),

            // Chart
            SizedBox(
              height: 300,
              child:
                  schoolStats.isEmpty
                      ? _buildEmptyState()
                      : _buildChart(context),
            ),
          ],
        ),
      ),
    );
  }

  /// Build empty state
  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.bar_chart_rounded, size: 48, color: Colors.black38),
          const SizedBox(height: 16),
          Text(
            'لا توجد بيانات متاحة',
            style: TextStyle(fontSize: 16, color: Colors.black38),
          ),
        ],
      ),
    );
  }

  /// Build chart
  Widget _buildChart(BuildContext context) {
    return BarChart(
      BarChartData(
        alignment: BarChartAlignment.spaceAround,
        maxY: _getMaxValue() * 1.2,
        barTouchData: BarTouchData(
          enabled: true,
          touchTooltipData: BarTouchTooltipData(
            tooltipBgColor: Colors.white,
            tooltipRoundedRadius: 8,
            tooltipBorder: BorderSide(color: Colors.grey.shade200, width: 1),
            tooltipPadding: const EdgeInsets.all(8),
            getTooltipItem: (group, groupIndex, rod, rodIndex) {
              final school = schoolStats[groupIndex];
              return BarTooltipItem(
                '${school.name}\n',
                TextStyle(
                  color: Colors.black87,
                  fontWeight: FontWeight.bold,
                  fontSize: 14,
                ),
                children: [
                  TextSpan(
                    text: 'الطلاب: ${school.studentCount}',
                    style: TextStyle(
                      color: Colors.black54,
                      fontSize: 12,
                      fontWeight: FontWeight.normal,
                    ),
                  ),
                ],
              );
            },
          ),
        ),
        titlesData: FlTitlesData(
          show: true,
          bottomTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              getTitlesWidget: (value, meta) {
                if (value >= schoolStats.length || value < 0) {
                  return const SizedBox();
                }

                final school = schoolStats[value.toInt()];
                final name =
                    school.name.length > 10
                        ? '${school.name.substring(0, 10)}...'
                        : school.name;

                return Padding(
                  padding: const EdgeInsets.only(top: 8),
                  child: Text(
                    name,
                    style: TextStyle(
                      color: Colors.black54,
                      fontSize: ResponsiveUtils.getResponsiveFontSizeByDevice(
                        context,
                        mobile: 10,
                        tablet: 12,
                        desktop: 14,
                      ),
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                );
              },
              reservedSize: 30,
            ),
          ),
          leftTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              getTitlesWidget: (value, meta) {
                return Text(
                  value.toInt().toString(),
                  style: TextStyle(
                    color: Colors.black54,
                    fontSize: ResponsiveUtils.getResponsiveFontSizeByDevice(
                      context,
                      mobile: 10,
                      tablet: 12,
                      desktop: 14,
                    ),
                    fontWeight: FontWeight.w500,
                  ),
                );
              },
              reservedSize: 30,
            ),
          ),
          topTitles: const AxisTitles(
            sideTitles: SideTitles(showTitles: false),
          ),
          rightTitles: const AxisTitles(
            sideTitles: SideTitles(showTitles: false),
          ),
        ),
        gridData: FlGridData(
          show: true,
          drawVerticalLine: false,
          getDrawingHorizontalLine: (value) {
            return FlLine(
              color: Colors.grey.shade200,
              strokeWidth: 1,
              dashArray: [5, 5],
            );
          },
        ),
        borderData: FlBorderData(show: false),
        barGroups: _getBarGroups(),
      ),
    );
  }

  /// Get max value for chart
  double _getMaxValue() {
    if (schoolStats.isEmpty) return 100;

    return schoolStats
        .map((stat) => stat.studentCount.toDouble())
        .reduce((a, b) => a > b ? a : b);
  }

  /// Get bar groups for chart
  List<BarChartGroupData> _getBarGroups() {
    return schoolStats.asMap().entries.map((entry) {
      final index = entry.key;
      final stat = entry.value;

      return BarChartGroupData(
        x: index,
        barRods: [
          BarChartRodData(
            toY: stat.studentCount.toDouble(),
            color: ColorConstants.primary,
            width: 16,
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(4),
              topRight: Radius.circular(4),
            ),
          ),
        ],
      );
    }).toList();
  }
}
