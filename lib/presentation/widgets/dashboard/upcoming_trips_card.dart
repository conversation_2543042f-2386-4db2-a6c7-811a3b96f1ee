import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../../core/constants/color_constants.dart';
import '../../../core/utils/responsive_utils.dart';
import '../../../domain/entities/trip.dart';

/// UpcomingTripsCard widget for displaying upcoming trips
/// Following Single Responsibility Principle by focusing only on upcoming trips UI
class UpcomingTripsCard extends StatelessWidget {
  final List<Trip> upcomingTrips;
  final VoidCallback? onViewAll;

  const UpcomingTripsCard({
    super.key,
    required this.upcomingTrips,
    this.onViewAll,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: BorderSide(color: Colors.grey.shade200, width: 1),
      ),
      color: Colors.white,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'الرحلات القادمة',
                  style: TextStyle(
                    fontSize: ResponsiveUtils.getResponsiveFontSizeByDevice(
                      context,
                      mobile: 16,
                      tablet: 18,
                      desktop: 20,
                    ),
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                  ),
                ),

                // View all button
                if (onViewAll != null)
                  TextButton(
                    onPressed: onViewAll,
                    child: Text(
                      'عرض الكل',
                      style: TextStyle(
                        fontSize: ResponsiveUtils.getResponsiveFontSizeByDevice(
                          context,
                          mobile: 12,
                          tablet: 14,
                          desktop: 16,
                        ),
                        color: ColorConstants.primary,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
              ],
            ),
            const SizedBox(height: 16),

            // Trips list
            upcomingTrips.isEmpty
                ? _buildEmptyState()
                : _buildTripsList(context),
          ],
        ),
      ),
    );
  }

  /// Build empty state
  Widget _buildEmptyState() {
    return SizedBox(
      height: 200,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.directions_bus_rounded, size: 48, color: Colors.black38),
            const SizedBox(height: 16),
            Text(
              'لا توجد رحلات قادمة',
              style: TextStyle(fontSize: 16, color: Colors.black38),
            ),
          ],
        ),
      ),
    );
  }

  /// Build trips list
  Widget _buildTripsList(BuildContext context) {
    return ListView.separated(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: upcomingTrips.length > 5 ? 5 : upcomingTrips.length,
      separatorBuilder:
          (context, index) => Divider(color: Colors.grey.shade200, height: 24),
      itemBuilder: (context, index) {
        final trip = upcomingTrips[index];
        return _buildTripItem(context, trip);
      },
    );
  }

  /// Build trip item
  Widget _buildTripItem(BuildContext context, Trip trip) {
    // Format date and time
    final dateFormat = DateFormat('yyyy-MM-dd');
    final timeFormat = DateFormat('HH:mm');
    final date = dateFormat.format(trip.startTime);
    final time = timeFormat.format(trip.startTime);

    return InkWell(
      onTap: () {
        // Navigate to trip details
      },
      borderRadius: BorderRadius.circular(8),
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 8),
        child: Row(
          children: [
            // Trip icon
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: ColorConstants.primary.withAlpha(26),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                Icons.directions_bus_rounded,
                color: ColorConstants.primary,
                size: ResponsiveUtils.getResponsiveIconSize(
                  context,
                  mobile: 20,
                  tablet: 24,
                  desktop: 28,
                ),
              ),
            ),
            const SizedBox(width: 12),

            // Trip details
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Trip name
                  Text(
                    trip.name,
                    style: TextStyle(
                      fontSize: ResponsiveUtils.getResponsiveFontSizeByDevice(
                        context,
                        mobile: 14,
                        tablet: 16,
                        desktop: 18,
                      ),
                      fontWeight: FontWeight.bold,
                      color: Colors.black87,
                    ),
                  ),
                  const SizedBox(height: 4),

                  // Trip details
                  Text(
                    '${trip.schoolName} - ${trip.busNumber}',
                    style: TextStyle(
                      fontSize: ResponsiveUtils.getResponsiveFontSizeByDevice(
                        context,
                        mobile: 12,
                        tablet: 14,
                        desktop: 16,
                      ),
                      color: Colors.black54,
                    ),
                  ),
                ],
              ),
            ),

            // Trip date and time
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                // Date
                Text(
                  date,
                  style: TextStyle(
                    fontSize: ResponsiveUtils.getResponsiveFontSizeByDevice(
                      context,
                      mobile: 12,
                      tablet: 14,
                      desktop: 16,
                    ),
                    fontWeight: FontWeight.w500,
                    color: Colors.black87,
                  ),
                ),
                const SizedBox(height: 4),

                // Time
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: ColorConstants.primary.withAlpha(26),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    time,
                    style: TextStyle(
                      fontSize: ResponsiveUtils.getResponsiveFontSizeByDevice(
                        context,
                        mobile: 10,
                        tablet: 12,
                        desktop: 14,
                      ),
                      fontWeight: FontWeight.bold,
                      color: ColorConstants.primary,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
