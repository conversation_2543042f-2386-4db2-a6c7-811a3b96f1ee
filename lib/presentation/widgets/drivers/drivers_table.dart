import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/constants/color_constants.dart';
import '../../../core/utils/responsive_utils.dart';
import '../../../domain/entities/driver.dart';
import '../../controllers/drivers_controller.dart';
import '../../routes/app_routes.dart';

/// DriversTable widget for displaying drivers in a table format
/// Following Single Responsibility Principle by focusing only on table UI
class DriversTable extends StatefulWidget {
  final double height;

  const DriversTable({
    super.key,
    required this.height,
  });

  @override
  State<DriversTable> createState() => _DriversTableState();
}

class _DriversTableState extends State<DriversTable> {
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 200) {
      final controller = Get.find<DriversController>();
      controller.loadMoreDrivers();
    }
  }

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<DriversController>();
    final isDesktop = ResponsiveUtils.isDesktop(context);

    return Obx(() {
      if (controller.isLoading && controller.drivers.isEmpty) {
        return _buildLoadingState();
      }

      if (controller.errorMessage.isNotEmpty && controller.drivers.isEmpty) {
        return _buildErrorState(controller);
      }

      if (controller.drivers.isEmpty) {
        return _buildEmptyState();
      }

      return Column(
        children: [
          // Table header
          _buildTableHeader(context, isDesktop),
          
          // Divider
          Divider(
            height: 1,
            color: Colors.grey.shade200,
          ),
          
          // Table content
          Expanded(
            child: ListView.builder(
              controller: _scrollController,
              padding: EdgeInsets.zero,
              itemCount: controller.drivers.length + (controller.isLoadingMore ? 1 : 0),
              itemBuilder: (context, index) {
                if (index == controller.drivers.length) {
                  return _buildLoadingMoreIndicator();
                }
                
                final driver = controller.drivers[index];
                return _buildDriverRow(context, driver, index, isDesktop);
              },
            ),
          ),
        ],
      );
    });
  }

  /// Build table header
  Widget _buildTableHeader(BuildContext context, bool isDesktop) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        border: Border(
          bottom: BorderSide(color: Colors.grey.shade200),
        ),
      ),
      child: Row(
        children: [
          // Name column
          Expanded(
            flex: 3,
            child: Text(
              'الاسم',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: ColorConstants.textPrimary,
              ),
            ),
          ),
          
          if (isDesktop) ...[
            // Username column
            Expanded(
              flex: 2,
              child: Text(
                'اسم المستخدم',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: ColorConstants.textPrimary,
                ),
              ),
            ),
            
            // Phone column
            Expanded(
              flex: 2,
              child: Text(
                'رقم الهاتف',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: ColorConstants.textPrimary,
                ),
              ),
            ),
            
            // Bus column
            Expanded(
              flex: 2,
              child: Text(
                'الحافلة',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: ColorConstants.textPrimary,
                ),
              ),
            ),
          ],
          
          // Actions column
          const SizedBox(width: 140), // Actions column
        ],
      ),
    );
  }

  /// Build driver row
  Widget _buildDriverRow(BuildContext context, Driver driver, int index, bool isDesktop) {
    final isEven = index % 2 == 0;
    
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: isEven ? Colors.white : Colors.grey.shade50,
        border: Border(
          bottom: BorderSide(
            color: Colors.grey.shade100,
            width: 0.5,
          ),
        ),
      ),
      child: Row(
        children: [
          // Name column with avatar
          Expanded(
            flex: 3,
            child: Row(
              children: [
                // Avatar
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        ColorConstants.primary,
                        ColorConstants.primary.withValues(alpha: 0.8),
                      ],
                    ),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Center(
                    child: Text(
                      driver.name?.isNotEmpty == true
                          ? driver.name![0].toUpperCase()
                          : 'S',
                      style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                
                // Name and details
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        driver.name ?? 'غير محدد',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          color: ColorConstants.textPrimary,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      if (!isDesktop && driver.phone != null) ...[
                        const SizedBox(height: 2),
                        Text(
                          driver.phone!,
                          style: TextStyle(
                            fontSize: 12,
                            color: ColorConstants.textSecondary,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ],
                  ),
                ),
              ],
            ),
          ),
          
          if (isDesktop) ...[
            // Username column
            Expanded(
              flex: 2,
              child: Text(
                driver.username ?? 'غير محدد',
                style: TextStyle(
                  fontSize: 14,
                  color: ColorConstants.textSecondary,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            
            // Phone column
            Expanded(
              flex: 2,
              child: Text(
                driver.phone ?? 'غير محدد',
                style: TextStyle(
                  fontSize: 14,
                  color: ColorConstants.textSecondary,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            
            // Bus column
            Expanded(
              flex: 2,
              child: driver.busName != null
                  ? Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: ColorConstants.primary.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: ColorConstants.primary.withValues(alpha: 0.3),
                        ),
                      ),
                      child: Text(
                        driver.busName!,
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                          color: ColorConstants.primary,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    )
                  : Text(
                      'غير مخصص',
                      style: TextStyle(
                        fontSize: 14,
                        color: ColorConstants.textSecondary,
                      ),
                    ),
            ),
          ],
          
          // Actions
          SizedBox(
            width: 140,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                // View details button
                IconButton(
                  onPressed: () => _showDriverDetails(context, driver),
                  icon: Icon(
                    Icons.visibility_outlined,
                    color: ColorConstants.primary,
                    size: 18,
                  ),
                  tooltip: 'عرض التفاصيل',
                  padding: const EdgeInsets.all(8),
                  constraints: const BoxConstraints(
                    minWidth: 36,
                    minHeight: 36,
                  ),
                ),

                // Edit button
                IconButton(
                  onPressed: () => _editDriver(driver),
                  icon: Icon(
                    Icons.edit_outlined,
                    color: Colors.orange,
                    size: 18,
                  ),
                  tooltip: 'تعديل السائق',
                  padding: const EdgeInsets.all(8),
                  constraints: const BoxConstraints(
                    minWidth: 36,
                    minHeight: 36,
                  ),
                ),

                // Delete button
                IconButton(
                  onPressed: () => _deleteDriver(context, driver),
                  icon: Icon(
                    Icons.delete_outline_rounded,
                    color: Colors.red,
                    size: 18,
                  ),
                  tooltip: 'حذف السائق',
                  padding: const EdgeInsets.all(8),
                  constraints: const BoxConstraints(
                    minWidth: 36,
                    minHeight: 36,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Build loading state
  Widget _buildLoadingState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(ColorConstants.primary),
          ),
          const SizedBox(height: 16),
          Text(
            'جاري تحميل السائقين...',
            style: TextStyle(
              fontSize: 16,
              color: ColorConstants.textSecondary,
            ),
          ),
        ],
      ),
    );
  }

  /// Build error state
  Widget _buildErrorState(DriversController controller) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline_rounded,
            size: 64,
            color: Colors.red.shade300,
          ),
          const SizedBox(height: 16),
          Text(
            'حدث خطأ في تحميل السائقين',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: ColorConstants.textPrimary,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            controller.errorMessage,
            style: TextStyle(
              fontSize: 14,
              color: ColorConstants.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () => controller.refreshDrivers(),
            icon: const Icon(Icons.refresh_rounded),
            label: const Text('إعادة المحاولة'),
            style: ElevatedButton.styleFrom(
              backgroundColor: ColorConstants.primary,
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  /// Build empty state
  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.person_off_rounded,
            size: 64,
            color: Colors.grey.shade400,
          ),
          const SizedBox(height: 16),
          Text(
            'لا توجد سائقين',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: ColorConstants.textPrimary,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'لم يتم العثور على أي سائقين في النظام',
            style: TextStyle(
              fontSize: 14,
              color: ColorConstants.textSecondary,
            ),
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () => Get.toNamed(AppRoutes.addDriver),
            icon: const Icon(Icons.add_rounded),
            label: const Text('إضافة سائق جديد'),
            style: ElevatedButton.styleFrom(
              backgroundColor: ColorConstants.primary,
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  /// Build loading more indicator
  Widget _buildLoadingMoreIndicator() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Center(
        child: CircularProgressIndicator(
          valueColor: AlwaysStoppedAnimation<Color>(ColorConstants.primary),
        ),
      ),
    );
  }

  /// Show driver details
  void _showDriverDetails(BuildContext context, Driver driver) {
    Get.toNamed(AppRoutes.driverDetails, arguments: driver);
  }

  /// Edit driver
  void _editDriver(Driver driver) {
    Get.toNamed(AppRoutes.editDriver, arguments: driver);
  }

  /// Delete driver with confirmation
  void _deleteDriver(BuildContext context, Driver driver) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('تأكيد الحذف'),
          content: Text('هل أنت متأكد من حذف السائق "${driver.name}"؟'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () async {
                Navigator.of(context).pop();
                final controller = Get.find<DriversController>();
                if (driver.id != null) {
                  await controller.deleteDriver(driver.id!);
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
              ),
              child: const Text('حذف'),
            ),
          ],
        );
      },
    );
  }
}
