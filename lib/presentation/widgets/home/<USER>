import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../core/constants/color_constants.dart';
import '../../../core/utils/responsive_utils.dart';
import '../../controllers/home_controller.dart';

/// HomeHeader widget for displaying greeting and profile info
/// Following Single Responsibility Principle by focusing only on header UI
class HomeHeader extends StatelessWidget {
  const HomeHeader({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<HomeController>();
    final hour = DateTime.now().hour;
    
    String greeting = '';
    if (hour >= 5 && hour < 12) {
      greeting = 'صباح الخير';
    } else if (hour >= 12 && hour < 17) {
      greeting = 'مساء الخير';
    } else {
      greeting = 'مساء الخير';
    }

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        // Greeting and name
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              greeting,
              style: TextStyle(
                fontSize: ResponsiveUtils.getResponsiveFontSizeByDevice(
                  context,
                  mobile: 18,
                  tablet: 20,
                  desktop: 22,
                ),
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
            ),
            const SizedBox(height: 4),
            Obx(() => Text(
              controller.userName,
              style: TextStyle(
                fontSize: ResponsiveUtils.getResponsiveFontSizeByDevice(
                  context,
                  mobile: 14,
                  tablet: 16,
                  desktop: 18,
                ),
                color: Colors.black54,
              ),
            )),
          ],
        ),

        // Action buttons
        Row(
          children: [
            // Refresh button
            IconButton(
              onPressed: () {
                // Refresh home data
                controller.refreshHomeData();
              },
              icon: Icon(
                Icons.refresh_rounded,
                color: ColorConstants.primary,
                size: ResponsiveUtils.getResponsiveIconSize(
                  context,
                  mobile: 24,
                  tablet: 28,
                  desktop: 32,
                ),
              ),
              tooltip: 'تحديث',
            ),

            // Notifications button
            IconButton(
              onPressed: () {
                // Show notifications
                Get.toNamed('/notifications');
              },
              icon: Icon(
                Icons.notifications_outlined,
                color: ColorConstants.primary,
                size: ResponsiveUtils.getResponsiveIconSize(
                  context,
                  mobile: 24,
                  tablet: 28,
                  desktop: 32,
                ),
              ),
              tooltip: 'الإشعارات',
            ),

            // Profile button
            InkWell(
              onTap: () {
                // Navigate to profile
                Get.toNamed('/profile');
              },
              child: CircleAvatar(
                radius: ResponsiveUtils.getResponsiveIconSize(
                  context,
                  mobile: 16,
                  tablet: 20,
                  desktop: 24,
                ),
                backgroundColor: ColorConstants.primary,
                child: const Icon(
                  Icons.person_outline_rounded,
                  color: Colors.white,
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }
}
