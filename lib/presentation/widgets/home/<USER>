import 'package:flutter/material.dart';

import '../../../core/constants/color_constants.dart';
import '../../../core/utils/responsive_utils.dart';
import '../../controllers/home_controller.dart';

/// HomeGrid widget for displaying main navigation options
/// Following Single Responsibility Principle by focusing only on grid UI
class HomeGrid extends StatelessWidget {
  final HomeController controller;

  const HomeGrid({
    super.key,
    required this.controller,
  });

  @override
  Widget build(BuildContext context) {
    final isDesktop =
        ResponsiveUtils.getDeviceType(context) == DeviceScreenType.desktop;
    final isTablet =
        ResponsiveUtils.getDeviceType(context) == DeviceScreenType.tablet;

    // Define grid items
    final List<_GridItem> gridItems = [
      _GridItem(
        icon: Icons.people_alt_rounded,
        title: 'الطلاب',
        onTap: controller.navigateToStudents,
        color: ColorConstants.primary,
      ),
      _GridItem(
        icon: Icons.supervisor_account_rounded,
        title: 'المشرفين',
        onTap: controller.navigateToSupervisors,
        color: ColorConstants.secondary,
      ),
      _GridItem(
        icon: Icons.directions_bus_rounded,
        title: 'الحافلات',
        onTap: controller.navigateToBuses,
        color: ColorConstants.accent1,
      ),
      _GridItem(
        icon: Icons.drive_eta_rounded,
        title: 'السائقين',
        onTap: controller.navigateToDrivers,
        color: ColorConstants.accent2,
      ),
      _GridItem(
        icon: Icons.family_restroom_rounded,
        title: 'أولياء الأمور',
        onTap: controller.navigateToParents,
        color: ColorConstants.primary,
      ),
      _GridItem(
        icon: Icons.location_on_rounded,
        title: 'طلبات تغيير العنوان',
        onTap: controller.navigateToAddressChangeRequests,
        color: ColorConstants.secondary,
      ),
      _GridItem(
        icon: Icons.history_rounded,
        title: 'الرحلات السابقة',
        onTap: controller.navigateToPreviousTrips,
        color: ColorConstants.accent1,
      ),
    ];

    // Calculate number of columns based on screen size
    int crossAxisCount = isDesktop ? 4 : (isTablet ? 3 : 2);

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: crossAxisCount,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
        childAspectRatio: 1.2,
      ),
      itemCount: gridItems.length,
      itemBuilder: (context, index) {
        final item = gridItems[index];
        return _buildGridItem(context, item);
      },
    );
  }

  Widget _buildGridItem(BuildContext context, _GridItem item) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: InkWell(
        onTap: item.onTap,
        borderRadius: BorderRadius.circular(16),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Icon
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: item.color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  item.icon,
                  color: item.color,
                  size: ResponsiveUtils.getResponsiveIconSize(
                    context,
                    mobile: 24,
                    tablet: 28,
                    desktop: 32,
                  ),
                ),
              ),
              const SizedBox(height: 12),
              
              // Title
              Text(
                item.title,
                style: TextStyle(
                  fontSize: ResponsiveUtils.getResponsiveFontSizeByDevice(
                    context,
                    mobile: 14,
                    tablet: 16,
                    desktop: 18,
                  ),
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ),
    );
  }
}

/// Grid item model
class _GridItem {
  final IconData icon;
  final String title;
  final VoidCallback onTap;
  final Color color;

  _GridItem({
    required this.icon,
    required this.title,
    required this.onTap,
    required this.color,
  });
}
