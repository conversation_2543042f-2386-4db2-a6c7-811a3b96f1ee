import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../core/constants/color_constants.dart';
import '../../../core/utils/responsive_utils.dart';
import '../../controllers/home_controller.dart';
import '../../routes/app_routes.dart';

/// HomeAddress widget for displaying user address
/// Following Single Responsibility Principle by focusing only on address UI
class HomeAddress extends StatelessWidget {
  const HomeAddress({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<HomeController>();

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(13),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Row(
        children: [
          // Location icon
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: ColorConstants.primary.withAlpha(26),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(
              Icons.location_on_outlined,
              color: ColorConstants.primary,
              size: ResponsiveUtils.getResponsiveIconSize(
                context,
                mobile: 24,
                tablet: 28,
                desktop: 32,
              ),
            ),
          ),
          const SizedBox(width: 16),

          // Address text
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'العنوان',
                  style: TextStyle(
                    fontSize: ResponsiveUtils.getResponsiveFontSizeByDevice(
                      context,
                      mobile: 14,
                      tablet: 16,
                      desktop: 18,
                    ),
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                  ),
                ),
                const SizedBox(height: 4),
                Obx(
                  () => Text(
                    controller.userAddress.isEmpty
                        ? 'لا يوجد عنوان'
                        : controller.userAddress,
                    style: TextStyle(
                      fontSize: ResponsiveUtils.getResponsiveFontSizeByDevice(
                        context,
                        mobile: 12,
                        tablet: 14,
                        desktop: 16,
                      ),
                      color: Colors.black54,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          ),

          // Edit button
          IconButton(
            onPressed: () {
              // Navigate to edit address
              Get.toNamed(AppRoutes.editProfile);
            },
            icon: Icon(
              Icons.edit_outlined,
              color: ColorConstants.primary,
              size: ResponsiveUtils.getResponsiveIconSize(
                context,
                mobile: 20,
                tablet: 24,
                desktop: 28,
              ),
            ),
            tooltip: 'تعديل العنوان',
          ),
        ],
      ),
    );
  }
}
