import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/constants/color_constants.dart';
import '../../../core/utils/responsive_utils.dart';
import '../../controllers/auth_controller.dart';
import '../../controllers/theme_controller.dart';
import '../../routes/app_routes.dart';
import '../theme_toggle_button.dart';
import 'sidebar_menu_item.dart';

/// ResponsiveSidebar widget for responsive sidebar layout
/// Following Single Responsibility Principle by focusing only on sidebar layout
class ResponsiveSidebar extends StatelessWidget {
  final Widget child;

  const ResponsiveSidebar({super.key, required this.child});

  @override
  Widget build(BuildContext context) {
    final deviceType = ResponsiveUtils.getDeviceType(context);
    final isMobile = deviceType == DeviceScreenType.mobile;

    return Scaffold(
      // Show drawer button only on mobile
      appBar:
          isMobile
              ? AppBar(
                backgroundColor: Theme.of(context).brightness == Brightness.dark
                    ? ColorConstants.headerDark
                    : ColorConstants.primary,
                elevation: 0,
                title: Text(
                  'بساطي',
                  style: TextStyle(
                    color: ColorConstants.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 20,
                  ),
                ),
                centerTitle: true,
                leading: Builder(
                  builder:
                      (context) => IconButton(
                        icon: Icon(Icons.menu, color: ColorConstants.white),
                        onPressed: () {
                          Scaffold.of(context).openDrawer();
                        },
                      ),
                ),
                actions: [
                  // Theme toggle button
                  const ThemeToggleButton(),
                  // Notifications button
                  IconButton(
                    icon: Icon(
                      Icons.notifications_outlined,
                      color: ColorConstants.white,
                    ),
                    onPressed: () {
                      // Show notifications
                    },
                  ),
                ],
              )
              : null,

      // Drawer for mobile
      drawer: isMobile ? _buildSidebar(context) : null,

      // Main content
      body: Row(
        children: [
          // Sidebar for tablet and desktop
          if (!isMobile) _buildSidebar(context),

          // Main content
          Expanded(child: child),
        ],
      ),
    );
  }

  /// Build sidebar
  Widget _buildSidebar(BuildContext context) {
    final deviceType = ResponsiveUtils.getDeviceType(context);
    final isMobile = deviceType == DeviceScreenType.mobile;
    final authController = Get.find<AuthController>();
    final user = authController.user;

    // Determine sidebar width based on device type
    double sidebarWidth;
    if (deviceType == DeviceScreenType.desktop) {
      sidebarWidth = 280;
    } else if (deviceType == DeviceScreenType.tablet) {
      sidebarWidth = 240;
    } else {
      sidebarWidth = 280; // For drawer on mobile
    }

    // Create sidebar
    final sidebar = Container(
      width: sidebarWidth,
      color: Theme.of(context).brightness == Brightness.dark
          ? ColorConstants.sidebarDark
          : ColorConstants.white,
      child: Column(
        children: [
          // Header
          Container(
            padding: const EdgeInsets.all(16),
            color: Theme.of(context).brightness == Brightness.dark
                ? ColorConstants.headerDark
                : ColorConstants.primary,
            child: Column(
              children: [
                // Logo and app name
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // Logo
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Theme.of(context).brightness == Brightness.dark
                            ? ColorConstants.cardDark
                            : ColorConstants.white,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Icon(
                        Icons.directions_bus_rounded,
                        color: Theme.of(context).brightness == Brightness.dark
                            ? ColorConstants.primary
                            : ColorConstants.primary,
                        size: 24,
                      ),
                    ),
                    const SizedBox(width: 8),

                    // App name
                    Text(
                      'بساطي',
                      style: TextStyle(
                        color: ColorConstants.white,
                        fontWeight: FontWeight.bold,
                        fontSize: 20,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),

                // Theme toggle button
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const ThemeToggleButton(),
                  ],
                ),
                const SizedBox(height: 8),

                // User info
                if (user != null) ...[
                  // User avatar
                  CircleAvatar(
                    radius: 32,
                    backgroundColor: Theme.of(context).brightness == Brightness.dark
                        ? ColorConstants.cardDark
                        : ColorConstants.white,
                    child: Text(
                      user.name.isNotEmpty ? user.name[0].toUpperCase() : 'U',
                      style: TextStyle(
                        color: ColorConstants.primary,
                        fontWeight: FontWeight.bold,
                        fontSize: 24,
                      ),
                    ),
                  ),
                  const SizedBox(height: 8),

                  // User name
                  Text(
                    user.name,
                    style: TextStyle(
                      color: ColorConstants.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),

                  // User email
                  Text(
                    user.email,
                    style: TextStyle(
                      color: ColorConstants.white.withAlpha(204),
                      fontSize: 14,
                    ),
                  ),
                ],
              ],
            ),
          ),

          // Menu items
          Expanded(
            child: ListView(
              padding: EdgeInsets.zero,
              children: [
                // Dashboard
                SidebarMenuItem(
                  icon: Icons.dashboard_rounded,
                  title: 'لوحة التحكم',
                  isActive: Get.currentRoute == AppRoutes.home,
                  onTap: () {
                    if (Get.currentRoute != AppRoutes.home) {
                      Get.offAllNamed(AppRoutes.home);
                    }
                  },
                ),

                // Home Page
                SidebarMenuItem(
                  icon: Icons.home_rounded,
                  title: 'الصفحة الرئيسية',
                  isActive: Get.currentRoute == AppRoutes.homePage,
                  onTap: () {
                    if (Get.currentRoute != AppRoutes.homePage) {
                      Get.toNamed(AppRoutes.homePage);
                    }
                  },
                ),

                // Students
                SidebarMenuItem(
                  icon: Icons.people_alt_rounded,
                  title: 'الطلاب',
                  isActive: Get.currentRoute.startsWith(AppRoutes.students),
                  onTap: () => Get.toNamed(AppRoutes.students),
                ),

                // Buses
                SidebarMenuItem(
                  icon: Icons.directions_bus_rounded,
                  title: 'الحافلات',
                  isActive: Get.currentRoute.startsWith(AppRoutes.buses),
                  onTap: () => Get.toNamed(AppRoutes.buses),
                ),

                // Drivers
                SidebarMenuItem(
                  icon: Icons.person_rounded,
                  title: 'السائقين',
                  isActive: Get.currentRoute.startsWith(AppRoutes.drivers),
                  onTap: () => Get.toNamed(AppRoutes.drivers),
                ),

                // Supervisors
                SidebarMenuItem(
                  icon: Icons.supervisor_account_rounded,
                  title: 'المشرفين',
                  isActive: Get.currentRoute.startsWith(AppRoutes.supervisors),
                  onTap: () => Get.toNamed(AppRoutes.supervisors),
                ),

                // Trips
                SidebarMenuItem(
                  icon: Icons.map_rounded,
                  title: 'الرحلات',
                  isActive: Get.currentRoute.startsWith(AppRoutes.trips),
                  onTap: () => Get.toNamed(AppRoutes.trips),
                ),

                // Attendance
                SidebarMenuItem(
                  icon: Icons.fact_check_rounded,
                  title: 'الحضور',
                  isActive: Get.currentRoute.startsWith(AppRoutes.attendance),
                  onTap: () => Get.toNamed(AppRoutes.attendance),
                ),

                // Parents
                SidebarMenuItem(
                  icon: Icons.family_restroom_rounded,
                  title: 'أولياء الأمور',
                  isActive: Get.currentRoute.startsWith(AppRoutes.parents),
                  onTap: () => Get.toNamed(AppRoutes.parents),
                ),

                // Address change requests
                SidebarMenuItem(
                  icon: Icons.location_on_rounded,
                  title: 'طلبات تغيير العنوان',
                  isActive: Get.currentRoute.startsWith(
                    AppRoutes.addressChangeRequests,
                  ),
                  onTap: () => Get.toNamed(AppRoutes.addressChangeRequests),
                ),

                // Previous trips
                SidebarMenuItem(
                  icon: Icons.history_rounded,
                  title: 'الرحلات السابقة',
                  isActive: Get.currentRoute.startsWith(
                    AppRoutes.previousTrips,
                  ),
                  onTap: () => Get.toNamed(AppRoutes.previousTrips),
                ),

                // Divider
                Divider(
                  color: Theme.of(context).brightness == Brightness.dark
                      ? ColorConstants.textSecondaryDark.withAlpha(80)
                      : Colors.grey.shade200,
                  height: 32,
                  thickness: 1,
                ),

                // Profile
                SidebarMenuItem(
                  icon: Icons.person_outline_rounded,
                  title: 'الملف الشخصي',
                  isActive: Get.currentRoute == AppRoutes.profile,
                  onTap: () => Get.toNamed(AppRoutes.profile),
                ),

                // Settings
                SidebarMenuItem(
                  icon: Icons.settings_outlined,
                  title: 'الإعدادات',
                  isActive: Get.currentRoute == AppRoutes.settings,
                  onTap: () => Get.toNamed(AppRoutes.settings),
                ),

                // Logout
                SidebarMenuItem(
                  icon: Icons.logout_rounded,
                  title: 'تسجيل الخروج',
                  isActive: false,
                  onTap: () async {
                    final success = await authController.logout();
                    if (success) {
                      Get.offAllNamed(AppRoutes.login);
                    }
                  },
                  textColor: Colors.red,
                  iconColor: Colors.red,
                ),
              ],
            ),
          ),
        ],
      ),
    );

    // Return as drawer for mobile, or as normal widget for tablet/desktop
    return isMobile ? Drawer(child: sidebar) : sidebar;
  }
}
