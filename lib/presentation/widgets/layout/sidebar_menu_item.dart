import 'package:flutter/material.dart';
import '../../../core/constants/color_constants.dart';

/// SidebarMenuItem widget for sidebar menu items
/// Following Single Responsibility Principle by focusing only on menu item UI
class SidebarMenuItem extends StatelessWidget {
  final IconData icon;
  final String title;
  final bool isActive;
  final bool isSelected;
  final VoidCallback onTap;
  final Color? textColor;
  final Color? iconColor;
  final Widget? trailing;

  const SidebarMenuItem({
    super.key,
    required this.icon,
    required this.title,
    required this.isActive,
    this.isSelected = false,
    required this.onTap,
    this.textColor,
    this.iconColor,
    this.trailing,
  });

  @override
  Widget build(BuildContext context) {
    // Determine colors based on active state
    final activeColor = ColorConstants.primary;
    final inactiveTextColor = textColor ?? Colors.black87;
    final inactiveIconColor = iconColor ?? Colors.black54;

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
      decoration: BoxDecoration(
        color: isActive ? activeColor.withAlpha(26) : Colors.transparent,
        borderRadius: BorderRadius.circular(8),
      ),
      child: ListTile(
        leading: Icon(
          icon,
          color: isActive ? activeColor : inactiveIconColor,
          size: 24,
        ),
        title: Text(
          title,
          style: TextStyle(
            color: isActive ? activeColor : inactiveTextColor,
            fontWeight: isActive ? FontWeight.bold : FontWeight.normal,
            fontSize: 16,
          ),
        ),
        trailing: trailing,
        onTap: onTap,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
        dense: true,
        visualDensity: const VisualDensity(horizontal: 0, vertical: -1),
      ),
    );
  }
}
