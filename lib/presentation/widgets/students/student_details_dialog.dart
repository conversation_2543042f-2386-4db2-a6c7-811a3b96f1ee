import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../core/constants/color_constants.dart';
import '../../../core/utils/responsive_utils.dart';
import '../../../domain/entities/student.dart';
import '../common/custom_network_image.dart';

/// Student details dialog
class StudentDetailsDialog extends StatelessWidget {
  final Student student;

  const StudentDetailsDialog({super.key, required this.student});

  @override
  Widget build(BuildContext context) {
    final isDesktop = ResponsiveUtils.isDesktop(context);
    final size = MediaQuery.of(context).size;

    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        width: isDesktop ? size.width * 0.6 : size.width * 0.9,
        height: isDesktop ? size.height * 0.8 : size.height * 0.9,
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(context),
            const SizedBox(height: 24),
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildStudentInfo(context),
                    const SizedBox(height: 24),
                    _buildParentsInfo(context),
                    const SizedBox(height: 24),
                    _buildSchoolInfo(context),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            _buildActions(context),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Row(
      children: [
        _buildStudentAvatar(),
        const SizedBox(width: 16),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                student.name ?? '',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  color: ColorConstants.primary,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Text(
                student.id ?? '',
                style: Theme.of(
                  context,
                ).textTheme.titleMedium?.copyWith(color: Colors.grey),
              ),
            ],
          ),
        ),
        IconButton(
          icon: const Icon(Icons.close),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ],
    );
  }

  Widget _buildStudentAvatar() {
    return CircleAvatar(
      radius: 40,
      backgroundColor: ColorConstants.primary.withAlpha(25),
      child:
          student.logo != null && student.logo!.isNotEmpty
              ? ClipRRect(
                borderRadius: BorderRadius.circular(40),
                child: CustomNetworkImage(
                  imageUrl: student.logo!,
                  width: 80,
                  height: 80,
                  fit: BoxFit.cover,
                ),
              )
              : Icon(Icons.person, color: ColorConstants.primary, size: 40),
    );
  }

  Widget _buildStudentInfo(BuildContext context) {
    return _buildSection(
      context,
      title: 'student_info'.tr,
      children: [
        _buildInfoRow(context, 'phone'.tr, student.phone ?? ''),
        _buildInfoRow(context, 'address'.tr, student.address ?? ''),
        _buildInfoRow(context, 'city'.tr, student.cityName ?? ''),
        _buildInfoRow(context, 'date_of_birth'.tr, student.dateBirth ?? ''),
        _buildInfoRow(context, 'gender'.tr, student.gender ?? ''),
        _buildInfoRow(context, 'religion'.tr, student.religion ?? ''),
        _buildInfoRow(context, 'blood_type'.tr, student.typeBlood?.name ?? ''),
      ],
    );
  }

  Widget _buildParentsInfo(BuildContext context) {
    final parents = student.parents ?? [];

    return _buildSection(
      context,
      title: 'parents_info'.tr,
      children:
          parents.isEmpty
              ? [
                Center(
                  child: Text(
                    'no_parents_found'.tr,
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                ),
              ]
              : parents.map((parent) {
                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      parent.name ?? '',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    _buildInfoRow(context, 'phone'.tr, parent.phone ?? ''),
                    _buildInfoRow(context, 'email'.tr, parent.email ?? ''),
                    _buildInfoRow(context, 'address'.tr, parent.address ?? ''),
                    const Divider(),
                  ],
                );
              }).toList(),
    );
  }

  Widget _buildSchoolInfo(BuildContext context) {
    return _buildSection(
      context,
      title: 'school_info'.tr,
      children: [
        _buildInfoRow(context, 'school'.tr, student.schools ?? ''),
        _buildInfoRow(context, 'grade'.tr, student.grade?.name ?? ''),
        _buildInfoRow(context, 'classroom'.tr, student.classroom ?? ''),
        _buildInfoRow(context, 'bus'.tr, student.bus?.name ?? ''),
        _buildInfoRow(context, 'bus_number'.tr, student.bus?.carNumber ?? ''),
        _buildInfoRow(context, 'trip_type'.tr, student.tripType ?? ''),
      ],
    );
  }

  Widget _buildSection(
    BuildContext context, {
    required String title,
    required List<Widget> children,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            color: ColorConstants.primary,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        ...children,
      ],
    );
  }

  Widget _buildInfoRow(BuildContext context, String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                color: Colors.grey[700],
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Text(
              value.isEmpty ? '-' : value,
              style: Theme.of(context).textTheme.titleSmall,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActions(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        ElevatedButton(
          onPressed: () => Navigator.of(context).pop(),
          style: ElevatedButton.styleFrom(
            backgroundColor: ColorConstants.primary,
            foregroundColor: Colors.white,
          ),
          child: Text('close'.tr),
        ),
      ],
    );
  }
}
