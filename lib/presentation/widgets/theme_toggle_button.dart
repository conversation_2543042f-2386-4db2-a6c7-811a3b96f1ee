import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../core/constants/color_constants.dart';
import '../controllers/theme_controller.dart';

/// ThemeToggleButton widget for toggling between light and dark themes
/// Following Single Responsibility Principle by focusing only on theme toggling
class ThemeToggleButton extends StatelessWidget {
  const ThemeToggleButton({super.key});

  @override
  Widget build(BuildContext context) {
    final ThemeController themeController = Get.find<ThemeController>();

    return Obx(() {
      final bool isDarkMode = themeController.isDarkMode;

      return Tooltip(
        message: themeController.themeTooltip,
        child: TweenAnimationBuilder<double>(
          tween: Tween<double>(begin: 0, end: isDarkMode ? 1.0 : 0.0),
          duration: const Duration(milliseconds: 300),
          builder: (context, value, child) {
            return Transform.rotate(
              angle: value * 0.5, // Rotate up to 180 degrees (π radians)
              child: IconButton(
                icon: AnimatedSwitcher(
                  duration: const Duration(milliseconds: 300),
                  transitionBuilder: (Widget child, Animation<double> animation) {
                    return FadeTransition(
                      opacity: animation,
                      child: ScaleTransition(
                        scale: animation,
                        child: child,
                      ),
                    );
                  },
                  child: isDarkMode
                      ? Icon(
                          Icons.dark_mode,
                          key: const ValueKey('dark'),
                          color: ColorConstants.complementaryColor,
                        )
                      : Icon(
                          Icons.light_mode,
                          key: const ValueKey('light'),
                          color: ColorConstants.highlightColor,
                        ),
                ),
                onPressed: () {
                  themeController.toggleTheme();
                },
              ),
            );
          },
        ),
      );
    });
  }
}
