import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:dio/dio.dart';

import 'package:school_web_app/core/network/api_service.dart';
import 'package:school_web_app/data/repositories/bus_repository_impl.dart';
import 'package:school_web_app/data/models/bus_model.dart';

// Generate mocks
@GenerateMocks([ApiService])
import 'bus_repository_test.mocks.dart';

void main() {
  group('BusRepositoryImpl', () {
    late BusRepositoryImpl repository;
    late MockApiService mockApiService;

    setUp(() {
      mockApiService = MockApiService();
      repository = BusRepositoryImpl(mockApiService);
    });

    group('getBuses', () {
      test('should return list of buses when API call is successful', () async {
        // Arrange
        final mockResponse = Response(
          data: {
            'status': true,
            'data': {
              'data': [
                {
                  'id': 1,
                  'name': 'Bus A1',
                  'car_number': 'ABC-123',
                  'notes': 'Test bus',
                  'created_at': '2024-01-01T00:00:00Z',
                  'updated_at': '2024-01-01T00:00:00Z',
                }
              ]
            }
          },
          statusCode: 200,
          requestOptions: RequestOptions(path: ''),
        );

        when(mockApiService.get(
          'buses/all',
          queryParameters: anyNamed('queryParameters'),
          isAuth: true,
        )).thenAnswer((_) async => mockResponse);

        // Act
        final result = await repository.getBuses();

        // Assert
        expect(result.isRight(), true);
        result.fold(
          (failure) => fail('Expected success but got failure: ${failure.message}'),
          (buses) {
            expect(buses.length, 1);
            expect(buses.first.name, 'Bus A1');
            expect(buses.first.carNumber, 'ABC-123');
          },
        );
      });

      test('should return failure when API call fails', () async {
        // Arrange
        final mockResponse = Response(
          data: {
            'status': false,
            'message': 'Failed to get buses',
          },
          statusCode: 400,
          requestOptions: RequestOptions(path: ''),
        );

        when(mockApiService.get(
          'buses/all',
          queryParameters: anyNamed('queryParameters'),
          isAuth: true,
        )).thenAnswer((_) async => mockResponse);

        // Act
        final result = await repository.getBuses();

        // Assert
        expect(result.isLeft(), true);
        result.fold(
          (failure) => expect(failure.message, 'Failed to get buses'),
          (buses) => fail('Expected failure but got success'),
        );
      });
    });

    group('createBus', () {
      test('should return created bus when API call is successful', () async {
        // Arrange
        final busToCreate = BusModel(
          name: 'New Bus',
          carNumber: 'NEW-123',
          notes: 'New test bus',
        );

        final mockResponse = Response(
          data: {
            'status': true,
            'data': {
              'id': 1,
              'name': 'New Bus',
              'car_number': 'NEW-123',
              'notes': 'New test bus',
              'created_at': '2024-01-01T00:00:00Z',
              'updated_at': '2024-01-01T00:00:00Z',
            }
          },
          statusCode: 201,
          requestOptions: RequestOptions(path: ''),
        );

        when(mockApiService.post(
          url: 'buses/store',
          body: anyNamed('body'),
          isAuth: true,
        )).thenAnswer((_) async => mockResponse);

        // Act
        final result = await repository.createBus(busToCreate);

        // Assert
        expect(result.isRight(), true);
        result.fold(
          (failure) => fail('Expected success but got failure: ${failure.message}'),
          (bus) {
            expect(bus.id, 1);
            expect(bus.name, 'New Bus');
            expect(bus.carNumber, 'NEW-123');
          },
        );
      });
    });
  });
}
